import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Logo from "../components/Logo";


const HomePage = () => {
  const [selectedIndex, setSelectedIndex] = useState(0); // Index de l'élément sélectionné
  const navigate = useNavigate();

  const menuItems = [
    { id: 0, label: "Login", icon: "/user.svg", action: () => navigate("/login") },
    { id: 1, label: "Language", icon: "/language.svg", action: () => navigate("/language") },
    { id: 2, label: "About", icon: "/about_app.svg", action: () => navigate("/about") },
  ];

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.keyCode === 39) {
      setSelectedIndex((prev) => (prev + 1) % menuItems.length); // Aller à droite
    } else if (event.keyCode === 37) {
      setSelectedIndex((prev) => (prev - 1 + menuItems.length) % menuItems.length); // Aller à gauche
    } else if (event.keyCode === 13) {
      menuItems[selectedIndex].action(); // Exécuter l'action de l'élément sélectionné
    }
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectedIndex]);

  return (
    <div className="flex h-screen bg-background text-white">
      <Logo />
      <div className="w-1/4 bg-sidebar flex items-center justify-center">
        <div className="text-4xl">Settings</div>
      </div>

      <div className="flex-1 flex flex-row justify-center items-center space-x-10">
        {menuItems.map((item, index) => (
          <div
            key={item.id}
            className="flex flex-col items-center"
          >
            <div
              className={`${selectedIndex === index
                ? "scale-110 bg-gray-700 rounded-full p-4"
                : "scale-100"
                }`}
            >
              <img src={item.icon} alt={`${item.label} Icon`} className="w-24 h-24" />
            </div>
            <button className="text-5xl mt-2">{item.label}</button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HomePage;