<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1674.38 686.3">
  <!-- Generator: Adobe Illustrator 29.3.0, SVG Export Plug-In . SVG Version: 2.1.0 Build 146)  -->
  <defs>
    <style>
      .st0 {
        display: none;
        font-family: VerminVibes2Soft, 'Vermin Vibes 2 Soft';
        font-size: 229px;
      }

      .st1 {
        font-family: MyriadPro-BoldIt, 'Myriad Pro';
        font-size: 295px;
        font-style: italic;
        font-weight: 700;
      }

      .st1, .st2 {
        fill: #fff;
      }

      .st3 {
        fill: #ec2024;
      }

      .st3, .st2, .st4 {
        fill-rule: evenodd;
      }

      .st5 {
        letter-spacing: 0em;
      }

      .st6 {
        fill: none;
      }

      .st7 {
        letter-spacing: .03em;
      }

      .st8 {
        clip-path: url(#clippath);
      }
    </style>
    <clipPath id="clippath">
      <rect class="st6" x="-118.04" y="-191.43" width="1920" height="1080"/>
    </clipPath>
  </defs>
  <g class="st8">
    <path class="st2" d="M429.83,477.88v-20.69c-.01-63.77.33-127.56-.28-191.33-.17-17.23,7.25-28.69,22.66-32.58,13.05-3.29,27.55-4.57,40.79-2.62,22.9,3.39,30.19,13.91,30.2,36.87.04,63.78.01,127.57.01,191.35v18.73h44.93c.99-1.88,1.95-2.88,1.95-3.89.11-73.62.43-147.25,0-220.87-.16-28.85-15.55-48.45-42.45-57.21-37.45-12.21-74.98-12.04-111.76,4.19-4.6,2.03-11.54,3.59-15.45,1.58-30.37-15.65-62.42-16.12-94.9-11.22-43.57,6.58-63.38,30.24-63.42,74.37-.04,64.94-.01,129.88-.01,194.82v17.82h46.68c0-6.99.01-12.7,0-18.4,0-62.62-.35-125.25.35-187.86.11-9.73,2.66-21.28,8.37-28.7,11.3-14.71,44.18-18.22,65.16-9.51,19.07,7.92,20.69,24.37,20.64,41.94-.15,61.46-.08,122.92-.03,184.38.01,6.11.51,12.21.81,18.83h45.73ZM1004.49,187.38c-10.31,0-17.77-.19-25.23.04-27.75.85-55.87-.7-83.19,3.32-50.97,7.5-67.21,28.29-67.33,79.56-.14,64.93-.06,129.87,0,194.79,0,4.39.51,8.79.77,13.24h47.66c0-7.52-.03-14.35,0-21.17.4-66.63.84-133.25,1.19-199.88.09-16.28,8.86-25.71,23.68-30.19,25.32-7.66,52.96-9.47,74.07-29.42-2.38,9.67-4.74,19.34-7.58,30.88h35.77v18.56c0,53.91-.07,107.83.06,161.75.03,6.29.31,12.79,1.85,18.85,8.82,34.72,48.81,56.04,92.78,48.99,0-7.78.11-15.78-.04-23.78-.08-4.52-.59-9.04-.98-14.49-3.41-.42-6.19-.61-8.91-1.11-30.44-5.62-36.76-13.18-36.76-43.87v-166.1h47.26c-.71-14-1.33-26.29-2.07-40.55h-45.47v-69.85h-47.53v70.45ZM738,295.03c-20.28,8.05-39.65,17.32-59.94,23.48-60.89,18.47-70.5,30.44-70.57,93.06,0,1.15-.01,2.32-.01,3.48,0,34.75,12.77,53.33,47.43,59.86,23.62,4.45,48.34,3.82,72.58,3.88,42.07.11,65.73-23.32,66.26-65.6.64-52.18.55-104.38.26-156.56-.18-31.59-17.35-54.17-47.46-62.84-30.15-8.68-60.73-8.75-90.72.09-37.64,11.11-53.23,35.72-47.45,73.82h43.73c9.08-35.78,44.76-52.19,74.75-34.11,9.02,5.43,18.61,18.36,19,28.21,1.93,48.63.93,97.38.79,146.09-.06,15.31-8.64,25.88-22.6,29.18-12.04,2.83-25.32,3.46-37.55,1.68-21.01-3.05-30.59-15.83-31.43-37.19-1.05-26.2,6.31-40.78,28.57-44.62,36.43-6.28,48.7-30.85,54.37-61.92"/>
    <path class="st4" d="M18.96,245.91c11.4-41.9,42.29-56.91,82.06-57.43,18.86-.25,38.34.94,56.54,5.5,36.58,9.17,51.33,32.36,48.58,72.16h-43.72c-12.52-33.92-30.56-43.77-65.08-35.67-20.48,4.8-29.65,14.39-30.64,32.03-1.22,21.38,5.61,32.45,25.71,39.82,22.81,8.37,45.85,16.11,68.79,24.19,29.91,10.54,44.63,30.68,45.78,62.47.04,1.16,0,2.32.11,3.48,5.82,60.02-20.79,86.06-84.39,89.66-10.96.62-22.16.71-32.98-.81-35.15-4.87-62.4-20.02-70.76-57.92v-24.36h45.52c8.85,36.64,18.98,43.89,55.79,42.2,24.27-1.12,35.13-9.72,39-30.89,4.04-22.17-4.45-38.05-26-46.48-17.71-6.93-35.54-13.94-53.85-18.92-30.81-8.38-52.46-25.35-60.46-57.27v-41.77Z"/>
    <path class="st4" d="M429.83,477.88h-45.74c-.3-6.61-.79-12.72-.81-18.83-.06-61.46-.13-122.92.03-184.38.04-17.57-1.57-34.02-20.64-41.93-20.99-8.71-53.86-5.2-65.16,9.51-5.71,7.42-8.26,18.97-8.37,28.7-.71,62.61-.35,125.24-.35,187.86.02,5.71,0,11.41,0,18.4h-46.68v-17.82c0-64.94-.03-129.88.01-194.82.04-44.13,19.85-67.79,63.42-74.37,32.48-4.9,64.53-4.43,94.9,11.22,3.91,2.01,10.85.45,15.45-1.58,36.79-16.23,74.31-16.4,111.76-4.19,26.9,8.77,42.29,28.36,42.45,57.21.42,73.62.11,147.25,0,220.87,0,1.01-.96,2.01-1.96,3.89h-44.93v-18.73c0-63.78.03-127.56-.01-191.35-.01-22.96-7.31-33.48-30.2-36.87-13.24-1.96-27.74-.67-40.79,2.62-15.41,3.88-22.83,15.35-22.66,32.57.61,63.78.27,127.56.28,191.33v20.69Z"/>
    <path class="st4" d="M1004.49,187.38v-70.44h47.53v69.85h45.47c.74,14.26,1.36,26.54,2.07,40.55h-47.27v166.1c0,30.7,6.32,38.25,36.76,43.87,2.72.5,5.49.69,8.91,1.11.38,5.45.89,9.97.97,14.49.16,7.99.04,15.99.04,23.78-43.97,7.05-83.95-14.27-92.78-48.99-1.54-6.05-1.83-12.55-1.85-18.85-.13-53.91-.06-107.83-.06-161.75v-18.56h-35.77c2.83-11.53,5.2-21.2,7.58-30.88-21.11,19.95-48.75,21.76-74.07,29.42-14.81,4.48-23.59,13.91-23.68,30.19-.35,66.63-.79,133.25-1.19,199.88-.03,6.83,0,13.66,0,21.18h-47.66c-.26-4.45-.76-8.85-.76-13.24-.06-64.93-.14-129.87,0-194.79.11-51.27,16.35-72.07,67.33-79.56,27.31-4.02,55.44-2.47,83.19-3.32,7.46-.23,14.92-.04,25.23-.04"/>
    <path class="st4" d="M738,295.03c-5.68,31.07-17.94,55.64-54.37,61.92-22.26,3.84-29.62,18.42-28.57,44.62.85,21.36,10.42,34.14,31.43,37.19,12.23,1.78,25.52,1.15,37.55-1.68,13.96-3.3,22.54-13.86,22.6-29.18.14-48.71,1.13-97.46-.79-146.09-.4-9.85-9.98-22.78-19-28.21-29.99-18.08-65.67-1.66-74.75,34.11h-43.73c-5.78-38.1,9.81-62.71,47.45-73.82,29.99-8.84,60.58-8.77,90.72-.09,30.12,8.67,47.28,31.25,47.46,62.84.3,52.19.38,104.39-.26,156.56-.52,42.27-24.19,65.71-66.26,65.6-24.24-.06-48.96.57-72.58-3.88-34.66-6.53-47.43-25.11-47.43-59.86,0-1.16.01-2.32.01-3.48.07-62.63,9.68-74.59,70.57-93.06,20.29-6.16,39.66-15.43,59.94-23.48"/>
    <path class="st3" d="M1664.96,349.74c-183.82-107.06-364.59-212.35-546.64-318.4v634.47c182.79-105.69,363.41-210.14,546.64-316.07"/>
  </g>
  <text class="st1" transform="translate(1087.96 398.42)"><tspan class="st7" x="0" y="0">T</tspan><tspan class="st5" x="165.49" y="0">V</tspan></text>
  <text class="st0" transform="translate(607.2 665.81)"><tspan x="0" y="0">MAX</tspan></text>
</svg>