import { useState, useEffect } from 'react';

export interface Language {
  code: string;
  label: string;
  flag: string;
}

export const languages: Language[] = [
  { code: 'fr', label: 'Français', flag: '🇫🇷' },
  { code: 'en', label: 'English', flag: '🇺🇸' },
  { code: 'ar', label: 'العربية', flag: '🇸🇦' },
  { code: 'it', label: 'Italiano', flag: '🇮🇹' },
  { code: 'es', label: 'Español', flag: '🇪🇸' },
  { code: 'de', label: 'Deutsch', flag: '🇩🇪' },
];

export const useLanguage = () => {
  const getDefaultLanguage = () => {
    // Essayer de détecter la langue du navigateur
    const browserLang = navigator.language.split('-')[0]; // 'fr-FR' -> 'fr'
    const supportedLanguages = ['fr', 'en', 'ar', 'it', 'es', 'de'];
    
    // Vérifier si la langue du navigateur est supportée
    if (supportedLanguages.includes(browserLang)) {
      return browserLang;
    }
    
    // Langue par défaut si non supportée
    return 'en';
  };

  const [currentLanguage, setCurrentLanguage] = useState<string>(() => {
    return localStorage.getItem('selectedLanguage') || getDefaultLanguage();
  });

  useEffect(() => {
    const handleLanguageChange = (event: CustomEvent) => {
      const language = event.detail;
      setCurrentLanguage(language.code);
    };

    window.addEventListener('languageChanged', handleLanguageChange as EventListener);
    
    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange as EventListener);
    };
  }, []);

  const changeLanguage = (languageCode: string) => {
    localStorage.setItem('selectedLanguage', languageCode);
    setCurrentLanguage(languageCode);
    window.dispatchEvent(new CustomEvent('languageChanged', { 
      detail: languages.find(lang => lang.code === languageCode) 
    }));
  };

  const getCurrentLanguage = () => {
    return languages.find(lang => lang.code === currentLanguage) || languages[0];
  };

  return {
    currentLanguage,
    changeLanguage,
    getCurrentLanguage,
    languages,
  };
};
