import { core, STORAGE_KEYS } from './core';

// Encrypt data before storing
export const encrypt = (data: string): string => {
  try {
    return btoa(encodeURIComponent(data));
  } catch (e) {
    console.error('Encryption error:', e);
    return '';
  }
};

// Decrypt stored data
export const decrypt = (data: string): string => {
  try {
    return decodeURIComponent(atob(data));
  } catch (e) {
    console.error('Decryption error:', e);
    return '';
  }
};

interface SessionData {
  pin: string;
  timestamp: number;
  deviceInfo: {
    uid: string;
    serial: string;
    model: string;
  };
  expiryMessage?: string;
  start?: string;
  end?: string;
}

export const storage = {
  // Save session data
  saveSession: (pin: string, expiryMessage?: string, start?: string, end?: string) => {
    const sessionData: SessionData = {
      pin,
      timestamp: Date.now(),
      deviceInfo: {
        uid: core.uid,
        serial: core.serial,
        model: core.model,
      },
      expiryMessage,
      start,
      end
    };
    try {
      localStorage.setItem(STORAGE_KEYS.SESSION, encrypt(JSON.stringify(sessionData)));
    } catch (e) {
      console.error('Failed to save session:', e);
    }
  },

  // Get session data
  getSession: (): SessionData | null => {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.SESSION);
      if (!data) return null;
      const decrypted = decrypt(data);
      if (!decrypted) return null;
      return JSON.parse(decrypted);
    } catch (e) {
      console.error('Failed to get session:', e);
      return null;
    }
  },

  // Clear session data
  clearSession: () => {
    try {
      localStorage.removeItem(STORAGE_KEYS.SESSION);
      localStorage.removeItem(STORAGE_KEYS.DEVICE);
      core.savedProfileLogin = '';
    } catch (e) {
      console.error('Failed to clear session:', e);
    }
  },

  // Save device info
  saveDeviceInfo: () => {
    try {
      const deviceInfo = {
        uid: core.uid,
        serial: core.serial,
        model: core.model,
      };
      localStorage.setItem(STORAGE_KEYS.DEVICE, encrypt(JSON.stringify(deviceInfo)));
    } catch (e) {
      console.error('Failed to save device info:', e);
    }
  },

  // Get device info
  getDeviceInfo: () => {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.DEVICE);
      if (!data) return null;
      const decrypted = decrypt(data);
      if (!decrypted) return null;
      return JSON.parse(decrypted);
    } catch (e) {
      console.error('Failed to get device info:', e);
      return null;
    }
  },

  // Get session expiry information
  getSessionExpiry: () => {
    const session = storage.getSession();
    if (!session) return null;

    return {
      start: session.start,
      end: session.end,
      expiryMessage: session.expiryMessage,
      isExpired: session.end ? new Date(session.end) < new Date() : false,
      daysRemaining: session.end ? Math.ceil((new Date(session.end).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : null
    };
  },
};

// Generic storage functions
export const getStorageItem = (key: string): string | null => {
  try {
    return localStorage.getItem(key);
  } catch (e) {
    console.error('Failed to get storage item:', e);
    return null;
  }
};

export const setStorageItem = (key: string, value: string): void => {
  try {
    localStorage.setItem(key, value);
  } catch (e) {
    console.error('Failed to set storage item:', e);
  }
};

export const removeStorageItem = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (e) {
    console.error('Failed to remove storage item:', e);
  }
};