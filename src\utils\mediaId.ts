  // Fonction helper pour obtenir un identifiant unique pour chaque type de média
  export const getMediaId = (item: any) => {
    if (item.id) {
      return item.id;
    }
    // Pour radio et TV qui n'ont pas d'ID, utiliser une combinaison name + link
    if (item.name && item.link) {
      return `${item.name}_${item.link}`.replace(/\s+/g, '_');
    }
    // Fallback sur le nom seul si pas de link
    return item.name || 'unknown';
  };