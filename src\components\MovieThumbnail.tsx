import React, { useEffect, useState } from "react";
import { getCachedData } from "../utils/tizenStorage";
import OptimizedImage from "./OptimizedImage";

export const MovieThumbnail = React.memo(({ sub, isSelected, progress, isFavorite, mediaType }: {
  sub: any,
  isSelected: boolean,
  progress?: number;
  isFavorite?: boolean;
  mediaType?: string;
}) => {
  const [progressPercent, setProgressPercent] = useState<number | null>(null);
  const [isNameTruncated, setIsNameTruncated] = useState(false);

  // Vérifier s'il existe une position de lecture sauvegardée
  useEffect(() => {
    const checkProgress = async () => {
      if (!sub || !sub.link) return;

      try {
        const savedPosition = await getCachedData<{
          position: number;
          timestamp: number;
          duration: number;
        }>(`movie_position_${sub.link}`);

        if (savedPosition && savedPosition.position > 0 && savedPosition.duration > 0) {
          // Calculer le pourcentage de progression
          const percent = Math.min(100, Math.round((savedPosition.position / savedPosition.duration) * 100));

          // Ne pas afficher si c'est presque terminé (>95%)
          if (percent < 95) {
            setProgressPercent(percent);
          }
        }
      } catch (error) {
        console.error('Error checking progress for movie:', error);
      }
    };

    checkProgress();
  }, [sub.id]); // Dépendance uniquement sur sub.id au lieu de sub entier

  // Déterminer si le nom doit être tronqué (plus de 22 caractères)
  useEffect(() => {
    if (sub?.name && sub.name.length > 22) {
      setIsNameTruncated(true);
    } else {
      setIsNameTruncated(false);
    }
  }, [sub?.name]);

  const containerStyle = mediaType === 'tv' 
    ? { height: '210px', width: '210px' }
    : { minHeight: '300px', minWidth: '200px' };

  return (
    <div
      className={`VodContainerBlocli ${isSelected ? "scale-110" : ""}`}
    >
      <div className= {`relative ${isSelected ? "VodContainerBlocScale" : ""}`} style={containerStyle}>
        {((sub.date_aj && (new Date().getTime() - new Date(sub.date_aj).getTime() < 2 * 24 * 60 * 60 * 1000)) ||
          (!sub.date_aj && sub.date && (new Date().getTime() - new Date(sub.date).getTime() < 2 * 24 * 60 * 60 * 1000))) && (
            <div className="texteFlag">New</div>
          )}

        {sub.rate && (
          <div className="texteRate">{sub.rate}</div>
        )}

        <OptimizedImage
          src={sub.logo.startsWith('/') ? `http://ll.ssdlist.xyz:2022/${sub.logo}` : sub.logo}
          alt={sub.name}
          className={`VodContainerBlocImg`}
          isSquare={mediaType === 'tv'}
        />

{progress !== undefined && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-600">
          <div 
            className="h-full bg-blue-500" 
            style={{ width: `${Math.min(100, progress * 100)}%` }}
          ></div>
        </div>
      )}
        {/* Barre de progression en dessous de l'image */}
        {progressPercent !== null && (
          <div className="w-full h-3 bg-gray-800 mb-1 overflow-hidden">
            <div
              className="h-full bg-red-600"
              style={{ width: `${progressPercent}%` }}
            ></div>
          </div>
        )}
      </div>


      <div
        className={`texteListePlay ${isSelected ? "texteListePlayScale" : ""}`}
      >
        <div className="flex items-center justify-center">
          {isFavorite && (
            <span className="text-yellow-400 mr-2" style={{ fontSize: '16px' }}>★</span>
          )}
          <div className="overflow-hidden relative" style={{ maxWidth: '200px' }}>
            {isSelected && isNameTruncated ? (
              <div className="animate-scroll">
                {sub.name}
              </div>
            ) : (
              <div className="text-center truncate">
                {isNameTruncated ? `${sub.name.substring(0, 22)}...` : sub.name}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});
