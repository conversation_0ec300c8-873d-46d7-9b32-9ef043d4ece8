import React from 'react';

type CategoriesFilterProps = {
  mediaCategories: string[];
  selectedZone: string;
  categoryBtnIndex: number;
  selectedSubCategory: string | null;
  onCategorySelect: (category: string) => void;
  onCategoryFocus: (index: number) => void;
  categoryButtonsRef: React.RefObject<HTMLButtonElement[]>;
};

export const CategoriesFilter: React.FC<CategoriesFilterProps> = ({
  mediaCategories,
  selectedZone,
  categoryBtnIndex,
  selectedSubCategory,
  onCategorySelect,
  onCategoryFocus,
  categoryButtonsRef
}) => {
  return (
    <div className="categories-container">
      <div className="categories-scroll">
        {mediaCategories.map((cat, i) => {
          const isFocused = selectedZone === "categories" && categoryBtnIndex === i;
          const isActive = selectedSubCategory === cat;
          
          return (
            <button
              key={i}
              ref={el => { 
                if (el && categoryButtonsRef.current) {
                  categoryButtonsRef.current[i] = el;
                }
              }}
              className={`category-btn ${isFocused ? 'focused' : ''} ${isActive ? 'active-category' : ''}`}
              onClick={() => onCategorySelect(cat)}
              onFocus={() => onCategoryFocus(i)}
            >
              {cat}
            </button>
          );
        })}
      </div>
    </div>
  );
};