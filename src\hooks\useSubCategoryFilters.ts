import { useState } from 'react';
import {SubCategory } from '../types/movies';

export const useSubCategoryFilters = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredSubcategories, setFilteredSubcategories] = useState<SubCategory[]>([]);
  const [selectedSubCategory, setSelectedSubCategory] = useState<string | null>(null);

  const filterBySearch = (query: string, subs: SubCategory[]) => {
    setSearchQuery(query);
    
    if (!query.trim()) {
      setFilteredSubcategories([]);
      return;
    }

    const results = subs.filter(sub =>
      sub.name.toLowerCase().includes(query.toLowerCase())
    );
    setFilteredSubcategories(results);
  };

  const filterByCategory = (category: string, subcategories: SubCategory[]) => {
    // Toggle si la même catégorie est sélectionnée
    if (selectedSubCategory === category) {
      setFilteredSubcategories([]);
      setSelectedSubCategory(null);
      return;
    }

    setSelectedSubCategory(category);
    
    const filtered = subcategories.filter(sub => {
      if (!sub.category) return false;
      
      const subcategoryCategories = sub.category.split(',').map(cat => cat.trim());
      return subcategoryCategories.includes(category);
    });

    setFilteredSubcategories(filtered);
  };

  const resetFilters = () => {
    setFilteredSubcategories([]);
    setSelectedSubCategory(null);
    setSearchQuery("");
  };

  return {
    searchQuery,
    filteredSubcategories,
    selectedSubCategory,
    filterBySearch,
    filterByCategory,
    resetFilters,
    setSearchQuery,
    setFilteredSubcategories
  };
};