import { useState, useEffect } from 'react';
import { getMediaId } from '../utils/mediaId';

export const useFavorites = (mediaType: string) => {
  const [favorites, setFavorites] = useState<any[]>([]);
  const [favoriteCategories, setFavoriteCategories] = useState<string[]>([]);

  // Charger les favoris
  const loadFavorites = () => {
    try {
      const savedFavorites = localStorage.getItem(`favorites_${mediaType}`);
      if (savedFavorites) {
        setFavorites(JSON.parse(savedFavorites));
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
    }
  };

  // Sauvegarder les favoris
  const saveFavorites = (updatedFavorites: any[]) => {
    try {
      localStorage.setItem(`favorites_${mediaType}`, JSON.stringify(updatedFavorites));
      setFavorites(updatedFavorites);
    } catch (error) {
      console.error('Error saving favorites:', error);
    }
  };

  // Charger les catégories favorites
  const loadFavoriteCategories = () => {
    try {
      const savedFavoriteCategories = localStorage.getItem(`favorite_categories_${mediaType}`);
      if (savedFavoriteCategories) {
        setFavoriteCategories(JSON.parse(savedFavoriteCategories));
      }
    } catch (error) {
      console.error('Error loading favorite categories:', error);
    }
  };

  // Sauvegarder les catégories favorites
  const saveFavoriteCategories = (updatedFavoriteCategories: string[], categories: any[], categoryIndex: number, setCategoryIndex: (index: number) => void, setExpandedCategory: (index: number) => void) => {
    try {
      localStorage.setItem(`favorite_categories_${mediaType}`, JSON.stringify(updatedFavoriteCategories));
      setFavoriteCategories(updatedFavoriteCategories);

      // Sauvegarder la catégorie actuellement sélectionnée
      const currentCategoryId = categories[categoryIndex]?.id;

      // Note: La réorganisation se fera automatiquement via le useEffect
      // qui écoute les changements de favoriteCategories

      // Retrouver la nouvelle position de la catégorie sélectionnée après réorganisation
      // Cette logique s'exécutera dans le prochain cycle de rendu
      setTimeout(() => {
        if (currentCategoryId) {
          const newIndex = categories.findIndex(cat => cat.id === currentCategoryId);
          if (newIndex >= 0) {
            setCategoryIndex(newIndex);
            setExpandedCategory(newIndex);
          }
        }
      }, 0);
    } catch (error) {
      console.error('Error saving favorite categories:', error);
    }
  };

  // Ajouter une catégorie aux favoris
  const addCategoryToFavorites = (categoryId: string) => {
    if (!favoriteCategories.includes(categoryId)) {
      const updatedFavoriteCategories = [...favoriteCategories, categoryId];
      setFavoriteCategories(updatedFavoriteCategories);
      localStorage.setItem(`favorite_categories_${mediaType}`, JSON.stringify(updatedFavoriteCategories));
      return true;
    }
    return false;
  };

  // Retirer une catégorie des favoris
  const removeCategoryFromFavorites = (categoryId: string) => {
    const updatedFavoriteCategories = favoriteCategories.filter(id => id !== categoryId);
    setFavoriteCategories(updatedFavoriteCategories);
    localStorage.setItem(`favorite_categories_${mediaType}`, JSON.stringify(updatedFavoriteCategories));
    return true;
  };

  // Ordonner les catégories avec les favorites en premier
  const orderCategoriesByFavorites = (allCategories: any[]) => {
    const specialCategories = allCategories.filter(cat => cat.isSpecial);
    const regularCategories = allCategories.filter(cat => !cat.isSpecial);

    const favoriteRegularCategories = regularCategories.filter(cat => favoriteCategories.includes(cat.id));
    const nonFavoriteRegularCategories = regularCategories.filter(cat => !favoriteCategories.includes(cat.id));

    return [...specialCategories, ...favoriteRegularCategories, ...nonFavoriteRegularCategories];
  };

  // Vérifier si les catégories sont dans le bon ordre
  const areCategoriesOrdered = (allCategories: any[]) => {
    const specialCategories = allCategories.filter(cat => cat.isSpecial);
    const regularCategories = allCategories.filter(cat => !cat.isSpecial);
    
    // Vérifier si les catégories favorites sont en premier après les spéciales
    let currentIndex = specialCategories.length;
    const favoriteRegularCategories = regularCategories.filter(cat => favoriteCategories.includes(cat.id));
    
    for (const favCat of favoriteRegularCategories) {
      if (allCategories[currentIndex]?.id !== favCat.id) {
        return false;
      }
      currentIndex++;
    }
    
    return true;
  };

  // Ajouter un élément aux favoris
  const addToFavorites = (item: any) => {
    const itemId = getMediaId(item);
    const isAlreadyFavorite = favorites.some(fav => getMediaId(fav) === itemId);
    if (!isAlreadyFavorite) {
      const updatedFavorites = [...favorites, { ...item, addedAt: new Date().toISOString() }];
      saveFavorites(updatedFavorites);
      return true;
    }
    return false;
  };

  // Retirer un élément des favoris
  const removeFromFavorites = (item: any) => {
    const itemId = getMediaId(item);
    const updatedFavorites = favorites.filter(fav => getMediaId(fav) !== itemId);
    saveFavorites(updatedFavorites);
    return true;
  };


  // Fonction helper pour vérifier si un élément est dans les favoris
  const isItemInFavorites = (item: any) => {
    const itemId = getMediaId(item);
    return favorites.some(fav => getMediaId(fav) === itemId);
  };

  // Charger les favoris au démarrage
  useEffect(() => {
    loadFavorites();
    loadFavoriteCategories();
  }, [mediaType]);

  return {
    favorites,
    favoriteCategories,
    loadFavorites,
    loadFavoriteCategories,
    saveFavoriteCategories,
    addCategoryToFavorites,
    removeCategoryFromFavorites,
    orderCategoriesByFavorites,
    areCategoriesOrdered,
    addToFavorites,
    removeFromFavorites,
    getMediaId,
    isItemInFavorites
  };
};
