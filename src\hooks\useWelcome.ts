import { useState, useCallback } from 'react';

interface WelcomeSettings {
  id: string;
  template: string;
  templateSplash: string;
}

interface WelcomeConfig {
  component: {
    welcome: WelcomeSettings;
  };
  language: {
    [key: string]: {
      welcome: string;
    };
  };
}

export const useWelcome = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentTitle, setCurrentTitle] = useState('');
  const [showSplash, setShowSplash] = useState(false);

  const init = useCallback((config: WelcomeConfig) => {
    try {
      // In a React context, we don't manipulate DOM directly
      // Instead, we manage state and let React handle the rendering
      const title = config.language['en']?.welcome || 'Welcome';
      setCurrentTitle(title);
      setIsVisible(true);
    } catch (error) {
      console.error('Welcome init error:', error);
    }
  }, []);

  const setPromoScreen = useCallback(() => {
    try {
      setShowSplash(true);
    } catch (error) {
      console.error('Welcome setPromoScreen error:', error);
    }
  }, []);

  const clear = useCallback(() => {
    try {
      setIsVisible(false);
      setShowSplash(false);
    } catch (error) {
      console.error('Welcome clear error:', error);
    }
  }, []);

  const generateHTML = useCallback((title: string) => {
    try {
      setCurrentTitle(title);
      setIsVisible(true);
    } catch (error) {
      console.error('Welcome generateHTML error:', error);
    }
  }, []);

  return {
    isVisible,
    currentTitle,
    showSplash,
    init,
    setPromoScreen,
    clear,
    generateHTML,
  };
};

export default useWelcome;
