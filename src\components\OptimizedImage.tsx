import { useState } from "react";

const OptimizedImage = ({ src, alt, className, isSquare }: { src: string, alt: string, className?: string, isSquare?: boolean }) => {
  const [imageSrc, setImageSrc] = useState(src);
  const [loaded, setLoaded] = useState(false);

  const imageStyle = isSquare 
    ? { width: '200px', height: '200px' }
    : { height: '300px', minWidth: '200px' };

  return (
    <div className={`relative ${className}`}>
      {/* Image de placeholder pendant le chargement */}
      {!loaded && (
        <div 
          className="absolute inset-0 flex items-center justify-center"
          style={imageStyle}
        >
          <img
            src="/loading_img.png"
            alt="Loading..."
            className="w-full h-full object-cover"
          />
        </div>
      )}
      
      <img
        src={imageSrc}
        alt={alt}
        style={imageStyle}
        className={`w-full h-full object-cover`}
        onLoad={() => setLoaded(true)}
        onError={() => setImageSrc('/NoLogo.png')}
        loading="lazy"
      />
    </div>
  );
};

export default OptimizedImage;