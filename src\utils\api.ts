import { AUTH_STATUS } from './core';
import { storage } from './storage';
import { Category, MovieApiResponse } from '../types/movies';
import { EpisodesApiResponse, SeriesApiResponse } from '../types/series';
import { RadioStationsResponse } from '../types/radio';
import { QuranSurahsResponse } from '../types/quran';
import { TVCategory, TVChannelsApiResponse } from '../types/tv';
import { ProfileData } from '../types/profile';
import { EventChannelsApiResponse, EventsApiResponse } from '../types/events';
import { getCachedData, setCachedData } from './tizenStorage';

export interface ApiResponse<T> {
  data: T;
  error?: string;
}

export interface CategoryApiResponse {
    packages: TVCategory[];
    category : Category[];
}

interface AuthResponse {
  authentification: Array<{
    status: string;
    msg: string;
    start?: string;
    end?: string;
  }>;
}

class ApiError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'ApiError';
  }
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://sg.xxttrrv.xyz';

const CACHE_KEYS = {
  PROFILE: 'profile_data',
  LIVE_TV: 'live_tv_data',
  LIVE_TV_CHANNELS: (categoryId: string) => `live_tv_channels_${categoryId}`,
  MOVIES: 'movies_data',
  MOVIES_BY_CATEGORY: (categoryId: string) => `movies_by_category_${categoryId}`,
  MOVIES_BY_MOVIES: (categoryId: string) => `movies_list_${categoryId}`,
  SERIES: 'series_data',
  // Ajoutez d'autres clés selon vos besoins
};

const createUrl = (endpoint: string, params: Record<string, string> = {}) => {
  const session = storage.getSession();
  const deviceParams = {
    login: session?.pin || '00', // Valeur par défaut pour login
    ...params,
  };

  const searchParams = new URLSearchParams(deviceParams);
  return `${API_BASE_URL}${endpoint}?${searchParams.toString()}`;
};

const defaultHeaders = {
  'Accept': 'application/json, text/plain, */*',
  'Content-Type': 'application/json',
  'X-Requested-With': 'XMLHttpRequest',
  'Cache-Control': 'no-cache',
  'Pragma': 'no-cache',
};

export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const decodeStreamUrl = (streamUrl: string, code: string): string => {
  try {
    const decodedUrl = atob(streamUrl);
    return `${decodedUrl}?code=${code}`;
  } catch {
    return streamUrl;
  }
};

const sanitizeUrl = (url: string): string => {
  if (!url) return '';
  
  let sanitized = url.replace(/\\/g, '').trim();
  
  if (!sanitized.startsWith('http://') && !sanitized.startsWith('https://')) {
    sanitized = `http://${sanitized}`;
  }
  
  try {
    const parsedUrl = new URL(sanitized);
    parsedUrl.pathname = encodeURI(parsedUrl.pathname);
    return parsedUrl.toString();
  } catch {
    return sanitized;
  }
};

const handleResponse = async <T>(response: Response): Promise<ApiResponse<T> | string> => {
  const text = await response.text();

  if (!response.ok) {
    if (text.trim().toLowerCase().startsWith('<!doctype html')) {
      throw new ApiError('The server is currently unavailable. Please try again later.', response.status);
    }
    
    try {
      const errorJson = JSON.parse(text);
      throw new ApiError(errorJson.error || errorJson.message || text, response.status);
    } catch (e) {
      throw new ApiError('Unexpected server response. Please try again later.', response.status);
    }
  }

  try {
    if (text.trim().toLowerCase().startsWith('<!doctype html')) {
      throw new ApiError('The server returned an invalid response. Please try again later.');
    }
    const data = JSON.parse(text);

    if ('coran' in data) {
      data.coran = data.coran.map((surah: any) => ({
        ...surah,
        link: sanitizeUrl(surah.link),
        logo: sanitizeUrl(surah.logo)
      }));
    }

    if ('radio' in data) {
      data.radio = data.radio.map((station: any) => ({
        ...station,
        link: sanitizeUrl(station.link),
        logo: sanitizeUrl(station.logo)
      }));
    }

    if ('channels' in data) {
      data.channels = data.channels.map((channel: any) => ({
        ...channel,
        logo: sanitizeUrl(channel.logo),
        ch: sanitizeUrl(channel.ch)
      }));
    }

    if ('movies' in data) {
      data.movies = data.movies.map((movie: any) => ({
        ...movie,
        thumbnail: sanitizeUrl(movie.thumbnail)
      }));
    }

    return { data };
  } catch (error) {
    throw new ApiError('Unable to process server response. Please try again later.');
  }
};

const handleApiError = (error: unknown, fallbackMessage: string): never => {
  if (!navigator.onLine) {
    throw new ApiError('No internet connection. Please check your network and try again.');
  }

  if (error instanceof ApiError) {
    throw error;
  }

  if (error instanceof Error) {
    throw new ApiError(error.message || fallbackMessage);
  }

  throw new ApiError(fallbackMessage);
};

export const api = {
  login: async (pin: string): Promise<AuthResponse | string> => {
    try {
      const url = createUrl('/act.php', { login: pin });
      
      const response = await fetch(url, {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      
      const result = await handleResponse<AuthResponse>(response);
      
      if (typeof result === 'string') {
        return result;
      }

      if (result.data.authentification[0].status === AUTH_STATUS.SUCCESS) {
        const authData = result.data.authentification[0];
        storage.saveSession(
          pin,
          authData.start,
          authData.end
        );
      }

      return result.data;
    } catch (error) {
      return handleApiError(error, 'Login failed. Please try again later.');
    }
  },

  fetchProfile: async (): Promise<ApiResponse<ProfileData> | string> => {
    try {
      const response = await fetch(createUrl('/profile.php'), {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      return handleResponse<ProfileData>(response);
    } catch (error) {
      return handleApiError(error, 'Failed to fetch profile data. Please try again later.');
    }
  },

  fetchLiveTV: async (): Promise<ApiResponse<CategoryApiResponse> | string> => {
    const cacheKey = CACHE_KEYS.LIVE_TV;
    
    try {
      const cachedData = await getCachedData<ApiResponse<CategoryApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      const response = await fetch(createUrl('/pack.php'), {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      
      const result = await handleResponse<CategoryApiResponse>(response);
      
      if (typeof result !== 'string') {
        await setCachedData(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      const cachedData = await getCachedData<ApiResponse<CategoryApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch Live TV data. Please try again later.');
    }
  },

  fetchLiveTVChannels: async (categoryId: string): Promise<ApiResponse<TVChannelsApiResponse> | string> => {
    const cacheKey = CACHE_KEYS.LIVE_TV_CHANNELS(categoryId);
    
    try {
      const cachedData = await getCachedData<ApiResponse<TVChannelsApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      const url = createUrl('/cha.php', { pack_id: categoryId });
      const response = await fetch(url, {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      
      const result = await handleResponse<TVChannelsApiResponse>(response);
      
      if (typeof result !== 'string') {
        await setCachedData(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      const cachedData = await getCachedData<ApiResponse<TVChannelsApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch Live TV channels. Please try again later.');
    }
  },

  fetchMovies: async (): Promise<ApiResponse<CategoryApiResponse> | string> => {
    const cacheKey = CACHE_KEYS.MOVIES;
    
    try {
      const cachedData = await getCachedData<ApiResponse<CategoryApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      const response = await fetch(createUrl('/moviescategory.php'), {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      
      const result = await handleResponse<CategoryApiResponse>(response);
      
      if (typeof result !== 'string') {
        await setCachedData(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      const cachedData = await getCachedData<ApiResponse<CategoryApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch Movies data. Please try again later.');
    }
  },


 fetchMoviesByMovies: async (categoryId: string): Promise<ApiResponse<MovieApiResponse> | string> => {
    const cacheKey = `movies_list_${categoryId}`;
    
    try {
      // Essayer de récupérer depuis le cache
      const cachedData = await getCachedData<ApiResponse<MovieApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Si pas dans le cache, faire l'appel API
      const url = createUrl('/movies.php', { pack_id: categoryId });
      const response = await fetch(url, {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      
      const result = await handleResponse<MovieApiResponse>(response);
      
      if (typeof result !== 'string') {
        // Mettre en cache
        await setCachedData(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      // En cas d'erreur, essayer de retourner le cache même expiré
      const cachedData = await getCachedData<ApiResponse<MovieApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch movies. Please try again later.');
    }
  },

  fetchMoviesByCategory: async (categoryId: string): Promise<ApiResponse<MovieApiResponse> | string> => {
    const cacheKey = `movies_category_${categoryId}`;
    
    try {
      // Vérifier le cache d'abord
      const cachedData = await getCachedData<ApiResponse<MovieApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Faire l'appel API si pas dans le cache
      const url = createUrl('/moviescategory.php', { pack_id: categoryId });
      const response = await fetch(url, {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      
      const result = await handleResponse<MovieApiResponse>(response);
      
      if (typeof result !== 'string') {
        // Sauvegarder dans le cache
        await setCachedData(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      // Fallback au cache en cas d'erreur
      const cachedData = await getCachedData<ApiResponse<MovieApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch movie categories. Please try again later.');
    }
  },

  fetchSeries: async (): Promise<ApiResponse<CategoryApiResponse> | string> => {
    const cacheKey = 'series_data';
    
    try {
      // Vérifier le cache en premier
      const cachedData = await getCachedData<ApiResponse<CategoryApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Faire l'appel API si nécessaire
      const response = await fetch(createUrl('/seriescategory.php'), {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      
      const result = await handleResponse<CategoryApiResponse>(response);
      
      if (typeof result !== 'string') {
        // Mettre à jour le cache
        await setCachedData(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      // Utiliser le cache comme fallback
      const cachedData = await getCachedData<ApiResponse<CategoryApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch Series data. Please try again later.');
    }
  },

   fetchSeriesBySeries: async (categoryId: string): Promise<ApiResponse<SeriesApiResponse> | string> => {
    const cacheKey = `series_list_${categoryId}`;
    
    try {
      // Essayer de récupérer depuis le cache
      const cachedData = await getCachedData<ApiResponse<SeriesApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Si pas dans le cache, faire l'appel API
      const url = createUrl('/seriescategory.php', { pack_id: categoryId });
      const response = await fetch(url, {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      
      const result = await handleResponse<SeriesApiResponse>(response);
      
      if (typeof result !== 'string') {
        // Mettre en cache
        await setCachedData(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      // En cas d'erreur, essayer de retourner le cache même expiré
      const cachedData = await getCachedData<ApiResponse<SeriesApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch movies. Please try again later.');
    }
  },

  fetchEpisodes: async (categoryId: string): Promise<ApiResponse<EpisodesApiResponse> | string> => {
    const cacheKey = `episodes_list_${categoryId}`;
    
    try {
      // Essayer de récupérer depuis le cache
      const cachedData = await getCachedData<ApiResponse<EpisodesApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // Si pas dans le cache, faire l'appel API
      const url = createUrl('/episodes.php', { pack_id: categoryId });
      const response = await fetch(url, {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      
      const result = await handleResponse<EpisodesApiResponse>(response);
      
      if (typeof result !== 'string') {
        // Mettre en cache
        await setCachedData(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      // En cas d'erreur, essayer de retourner le cache même expiré
      const cachedData = await getCachedData<ApiResponse<EpisodesApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch movies. Please try again later.');
    }
  },

  fetchRadio: async (): Promise<ApiResponse<CategoryApiResponse> | string> => {
    const cacheKey = 'radio_data';
    try {
      const cachedData = await getCachedData<ApiResponse<CategoryApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      const response = await fetch(createUrl('/radiocountry.php'), {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      const result = await handleResponse<CategoryApiResponse>(response);
      if (typeof result !== 'string') {
        await setCachedData(cacheKey, result);
      }
      return result;
    } catch (error) {
      const cachedData = await getCachedData<ApiResponse<CategoryApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch Radio data. Please try again later.');
    }
  },

  fetchRadioStations: async (categoryId: string): Promise<ApiResponse<RadioStationsResponse> | string> => {
    const cacheKey = `radio_stations_${categoryId}`;
    try {
      const cachedData = await getCachedData<ApiResponse<RadioStationsResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      const url = createUrl('/radio.php', { pack_id: categoryId });
      const response = await fetch(url, {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      const result = await handleResponse<RadioStationsResponse>(response);
      if (typeof result !== 'string') {
        await setCachedData(cacheKey, result);
      }
      return result;
    } catch (error) {
      const cachedData = await getCachedData<ApiResponse<RadioStationsResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch radio stations. Please try again later.');
    }
  },

  fetchQuran: async (): Promise<ApiResponse<CategoryApiResponse> | string> => {
    const cacheKey = 'quran_data';
    try {
      const cachedData = await getCachedData<ApiResponse<CategoryApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      const response = await fetch(createUrl('/coranpack.php'), {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      const result = await handleResponse<CategoryApiResponse>(response);
      if (typeof result !== 'string') {
        await setCachedData(cacheKey, result);
      }
      return result;
    } catch (error) {
      const cachedData = await getCachedData<ApiResponse<CategoryApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch Quran data. Please try again later.');
    }
  },

  fetchQuranSurahs: async (categoryId: string): Promise<ApiResponse<QuranSurahsResponse> | string> => {
    const cacheKey = `quran_surahs_${categoryId}`;
    try {
      const cachedData = await getCachedData<ApiResponse<QuranSurahsResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      const url = createUrl('/coran.php', { pack_id: categoryId });
      const response = await fetch(url, {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      const result = await handleResponse<QuranSurahsResponse>(response);
      if (typeof result !== 'string') {
        await setCachedData(cacheKey, result);
      }
      return result;
    } catch (error) {
      const cachedData = await getCachedData<ApiResponse<QuranSurahsResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch Quran surahs. Please try again later.');
    }
  },

  fetchEvents: async (): Promise<ApiResponse<EventsApiResponse> | string> => {
    const cacheKey = 'events_data';
    try {
      const cachedData = await getCachedData<ApiResponse<EventsApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      const response = await fetch(createUrl('/events.php'), {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      const result = await handleResponse<EventsApiResponse>(response);
      if (typeof result !== 'string') {
        await setCachedData(cacheKey, result);
      }
      return result;
    } catch (error) {
      const cachedData = await getCachedData<ApiResponse<EventsApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch events. Please try again later.');
    }
  },

  fetchEventsChannels: async (categoryId: string): Promise<ApiResponse<EventChannelsApiResponse> | string> => {
    const cacheKey = `events_channels_${categoryId}`;
    try {
      const cachedData = await getCachedData<ApiResponse<EventChannelsApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      const url = createUrl('/eventchannels.php', { pack_id: categoryId });
      const response = await fetch(url, {
        method: 'GET',
        headers: defaultHeaders,
        credentials: 'include',
      });
      const result = await handleResponse<EventChannelsApiResponse>(response);
      if (typeof result !== 'string') {
        await setCachedData(cacheKey, result);
      }
      return result;
    } catch (error) {
      const cachedData = await getCachedData<ApiResponse<EventChannelsApiResponse>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
      return handleApiError(error, 'Failed to fetch Events Channels. Please try again later.');
    }
  },

};
