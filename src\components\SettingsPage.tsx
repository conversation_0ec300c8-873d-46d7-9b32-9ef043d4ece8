import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useTranslation } from "../hooks/useTranslation";
import { storage } from "../utils/storage";

const SettingsPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Vérifier si l'utilisateur est connecté (état réactif)
  const [isUserLoggedIn, setIsUserLoggedIn] = useState(() => storage.getSession() !== null);

  const [selectedIndex, setSelectedIndex] = useState(() => {
    // Initialiser l'index basé sur le paramètre URL
    const item = searchParams.get('item');
    if (isUserLoggedIn) {
      switch(item) {
        case 'reset': return 0;
        case 'profile': return 1;
        case 'language': return 2;
        case 'about': return 3;
        default: return 0;
      }
    } else {
      switch(item) {
        case 'login': return 0;
        case 'language': return 1;
        case 'about': return 2;
        default: return 0;
      }
    }
  });

  const handleReset = () => {
    const confirmed = window.confirm(t("logoutConfirm"));
    if (confirmed) {
      storage.clearSession();
      navigate("/?section=settings");
    }
  };

  const handleLogin = () => {
    navigate("/login");
  };

  // Surveiller les changements de session
  useEffect(() => {
    const checkSession = () => {
      const sessionExists = storage.getSession() !== null;
      if (sessionExists !== isUserLoggedIn) {
        setIsUserLoggedIn(sessionExists);
        // Réinitialiser l'index sélectionné quand l'état de connexion change
        setSelectedIndex(0);
      }
    };

    // Vérifier immédiatement
    checkSession();

    // Vérifier périodiquement
    const interval = setInterval(checkSession, 500);

    return () => clearInterval(interval);
  }, [isUserLoggedIn]);

  // Définir les éléments de menu selon l'état de connexion
  const menuItems = isUserLoggedIn ? [
    { id: 0, label: t("reset"), icon: "/user_logout.svg", action: handleReset },
    { id: 1, label: t("profile"), icon: "/user.svg", action: () => navigate("/profile") },
    { id: 2, label: t("language"), icon: "/language.svg", action: () => navigate("/language") },
    { id: 3, label: t("about"), icon: "/about_app.svg", action: () => navigate("/about") },
  ] : [
    { id: 0, label: t("login"), icon: "/user.svg", action: handleLogin },
    { id: 1, label: t("language"), icon: "/language.svg", action: () => navigate("/language") },
    { id: 2, label: t("about"), icon: "/about_app.svg", action: () => navigate("/about") },
  ];

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "ArrowRight") {
      setSelectedIndex((prev) => (prev + 1) % menuItems.length);
    } else if (event.key === "ArrowLeft") {
      setSelectedIndex((prev) => (prev - 1 + menuItems.length) % menuItems.length);
    } else if (event.key === "Enter") {
      menuItems[selectedIndex].action();
    } else if (event.key === "Escape" || event.key === "Backspace") {
      navigate("/?section=settings");
    }
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectedIndex, menuItems]);

  return (
    <div className="flex h-screen bg-background text-white">
      <div className="flex-1 flex flex-row justify-center items-center space-x-16">
        {menuItems.map((item, index) => (
          <div
            key={item.id}
            className={`flex flex-col items-center transition-transform duration-200 ease-in-out ${selectedIndex === index
                ? "scale-110"
                : ""
              }`}
            style={{ transformOrigin: 'center' }}
          >
            <div className="relative mb-4">
              <div
                className={`${selectedIndex === index
                    ? "bg-gray-700 rounded-full absolute inset-0 w-32 h-32 -m-4"
                    : ""
                  }`}
              />
              <img src={item.icon} alt={`${item.label} Icon`} className="w-24 h-24 relative z-10" />
            </div>
            <button className="text-5xl mt-2">{item.label}</button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SettingsPage;