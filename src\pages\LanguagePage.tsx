import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "../hooks/useLanguage";
import { useTranslation } from "../hooks/useTranslation";
import { RETURN_KEY, RETURN_KEY_CODE } from "../utils/keysCode";

const LanguagePage = () => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const navigate = useNavigate();
  const { currentLanguage, changeLanguage, languages } = useLanguage();
  const { t } = useTranslation();

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "ArrowDown") {
      setSelectedIndex((prev) => (prev + 1) % languages.length);
    } else if (event.key === "ArrowUp") {
      setSelectedIndex((prev) => (prev - 1 + languages.length) % languages.length);
    } else if (event.key === "Enter") {
      handleLanguageSelect(languages[selectedIndex]);
    } else if (event.key === RETURN_KEY || event.keyCode === RETURN_KEY_CODE) {
      navigate("/?section=settings&item=language");
    }
  };

  const handleLanguageSelect = (language: { code: string; label: string; flag: string }) => {
    changeLanguage(language.code);
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectedIndex]);

  return (
    <div className="flex h-screen bg-background text-white">
      <div className="flex-1 flex flex-col justify-center items-center">
        <h1 className="text-6xl font-bold mb-12 text-center">
          {t("chooseLanguage")}
        </h1>
        
        <div className="flex flex-col space-y-6 w-full max-w-md">
          {languages.map((language, index) => (
            <div
              key={language.code}
              className={`flex items-center justify-between p-6 rounded-lg transition-all duration-200 cursor-pointer ${
                selectedIndex === index
                  ? "bg-white/20 scale-105 border-2 border-white/50"
                  : "bg-white/10 hover:bg-white/15"
              } ${
                currentLanguage === language.code
                  ? "ring-2 ring-blue-500"
                  : ""
              }`}
              onClick={() => handleLanguageSelect(language)}
            >
              <div className="flex items-center space-x-4">
                <span className="text-4xl">{language.flag}</span>
                <span className="text-3xl font-medium">{language.label}</span>
              </div>
              {currentLanguage === language.code && (
                <div className="text-2xl text-green-400">✓</div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LanguagePage;
