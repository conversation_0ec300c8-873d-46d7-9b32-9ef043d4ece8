import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { api } from '../utils/api';
import { CategoriesFilter } from '../components/MovieCategoriesFilter';
import useMediaCategories from '../hooks/useMediaCategories';
import { RadioStation } from '../types/radio';
import { Category, Movie } from '../types/movies';
import { Episode, SeriesCategory } from '../types/series';
import { useSubCategoryFilters } from '../hooks/useSubCategoryFilters';
import { MovieThumbnail } from '../components/MovieThumbnail';
import { useTranslation } from '../hooks/useTranslation';
import { useFavorites } from '../hooks/useFavorites';
import { useStillWatching } from '../hooks/useStillWatching';
import { useScrollArrows } from '../hooks/useScrollArrows';
import { RETURN_KEY, RETURN_KEY_CODE } from '../utils/keysCode';
import { MediaPageProps, Zone } from '../types/interface';

const MediaPage: React.FC<MediaPageProps> = ({ mediaType }) => {
  const [selectedZone, setSelectedZone] = useState<Zone>('left');
  const [subCategoryIndex, setSubCategoryIndex] = useState(0);
  const [searchFocused, setSearchFocused] = useState(false);
  const [categoryBtnIndex, setCategoryBtnIndex] = useState(0);
  const [expandedCategory, setExpandedCategory] = useState(0);
  const [categoryIndex, setCategoryIndex] = useState(0);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingSeries, setLoadingSeries] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeCategoryId, setActiveCategoryId] = useState<string | null>(null);
  const [initialCategorySelected, setInitialCategorySelected] = useState(false);
  const [loadedCategories, setLoadedCategories] = useState<Record<string, any[]>>({});
  const [navigationHistory, setNavigationHistory] = useState<{ level: string, index: number }[]>([]); const [currentLevel, setCurrentLevel] = useState<string>('main'); // 'main', 'series', 'season1', 'season2', 'episodes', 'subcategory', 'movie'
  const subCategoriesContainerRef = useRef<HTMLDivElement>(null);
  const categoriesContainerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchButtonRef = useRef<HTMLButtonElement>(null);
  const categoryButtonsRef = useRef<HTMLButtonElement[]>([]);  // États pour la gestion des favoris
  const enterPressStartRef = useRef<number | null>(null);
  const categoryEnterPressStartRef = useRef<number | null>(null); // Pour les catégories

  const {
    favorites,
    favoriteCategories,
    addCategoryToFavorites,
    removeCategoryFromFavorites,
    orderCategoriesByFavorites,
    areCategoriesOrdered,
    addToFavorites,
    removeFromFavorites,
    isItemInFavorites,
    getMediaId
  } = useFavorites(mediaType);

  const { stillWatchingItems } = useStillWatching(mediaType);

  // Hook pour la gestion des flèches de scroll
  const { showUpArrow, showDownArrow } = useScrollArrows(
    categoriesContainerRef,
    [categories, categoryIndex, selectedZone]
  );

  // État pour stocker toutes les sourates d'une catégorie (pour le Quran)
  const [allSurahs, setAllSurahs] = useState<any[]>([]);

  // État pour stocker l'état de navigation en attente de restauration
  const [pendingNavState, setPendingNavState] = useState<any>(null);

  // Limitation de navigation - anti-spam des touches de navigation pour la zone droite uniquement
  const lastNavigationTime = useRef<number>(0);
  const navigationCooldown = 330;
  const navigationCooldownRL = 200;

  const [loadedItemsCount, setLoadedItemsCount] = useState(90);
  const [allItemsLoaded,] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const {
    getCategories,
    fetchCategories
  } = useMediaCategories();

  const {
    searchQuery,
    filteredSubcategories,
    selectedSubCategory,
    filterBySearch,
    filterByCategory,
    setSearchQuery,
    resetFilters
  } = useSubCategoryFilters();

  const { t } = useTranslation();

  const mediaCategories = activeCategoryId ? getCategories(activeCategoryId) : [];

  // Récupération des catégories principales
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        let response;

        // Catégories spéciales
        const specialCategories: Category[] = [
          {
            id: 'favorites',
            name: t('favorites'),
            subcategories: [],
            isSpecial: true
          }
        ];

        // Ajouter "Still Watching" seulement pour movies, series et quran
        if (mediaType === 'movie' || mediaType === 'series' || mediaType === 'quran') {
          specialCategories.push({
            id: 'still-watching',
            name: t('stillWatching'),
            subcategories: [],
            isSpecial: true
          });
        }

        if (mediaType === 'series') {
          response = await api.fetchSeries();
        } else if (mediaType === 'movie') {
          response = await api.fetchMovies();
        } else if (mediaType === 'radio') {
          response = await api.fetchRadio();
        } else if (mediaType === 'quran') {
          response = await api.fetchQuran();
        } else if (mediaType === 'tv') {
          response = await api.fetchLiveTV();
        }

        if (typeof response === 'string' || !response) {
          setError(typeof response === 'string' ? response : `No response received for ${mediaType} categories.`);
          return;
        }

        let formattedCategories;

        if (mediaType === 'tv') {
          formattedCategories = (response.data?.packages || []).map(cat => ({
            ...cat,
            subcategories: [],
            type: mediaType
          }));
        } else {
          formattedCategories = (response.data?.category || []).map(cat => ({
            ...cat,
            subcategories: [],
            ...(mediaType === 'movie' && { movies: [] }),
            ...((mediaType === 'radio' || mediaType === 'quran') && { type: mediaType })
          }));
        }

        const allCategories = [...specialCategories, ...formattedCategories];

        // Ordonner les catégories : favorites en premier
        const orderedCategories = orderCategoriesByFavorites(allCategories);
        setCategories(orderedCategories);
        setError(null);
      } catch (err) {
        setError(`Failed to load ${mediaType} categories. Please try again later.`);
        console.error(`Error fetching ${mediaType} categories:`, err);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [mediaType]);


  const fetchSubCategories = async (categoryId: string, categoryType: string) => {
    try {
      // Vérifiez si les données sont déjà en cache
      if (loadedCategories[categoryId]) {
        // Utilisez les données en cache mais mettez à jour l'état de navigation
        if (currentLevel !== 'main') {
          setNavigationHistory([...navigationHistory, { level: 'main', index: categoryIndex }]);
        }

        setCurrentLevel(mediaType === 'series' ? 'series' : 'subcategory');
        setActiveCategoryId(categoryId);

        // Utilisez les données en cache pour mettre à jour les catégories
        setCategories(prevCategories =>
          prevCategories.map(cat =>
            cat.id === categoryId
              ? { ...cat, subcategories: loadedCategories[categoryId] }
              : cat
          )
        );

        return;
      }

      // Si les données ne sont pas en cache, affichez le chargement
      setLoading(true);

      // Save current state in history if not already at main level
      if (currentLevel !== 'main') {
        setNavigationHistory([...navigationHistory, { level: 'main', index: categoryIndex }]);
      }

      setCurrentLevel('series');
      let response: any;

      if (mediaType === 'series') {
        response = await api.fetchSeriesBySeries(categoryId);
        await fetchCategories(categoryId, 'series');
      } else if (mediaType === 'movie') {
        if (categoryType === "1") {
          response = await api.fetchMoviesByMovies(categoryId);
          await fetchCategories(categoryId, 'movie');
          setCurrentLevel('subcategory');
        } else {
          response = await api.fetchMoviesByCategory(categoryId);
          setCurrentLevel('subcategory');
        }
      } else if (mediaType === 'radio') {
        response = await api.fetchRadioStations(categoryId);
      } else if (mediaType === 'quran') {
        response = await api.fetchQuranSurahs(categoryId);
      } else if (mediaType === 'tv') {
        response = await api.fetchLiveTVChannels(categoryId);
      }

      setActiveCategoryId(categoryId);

      if (typeof response === 'string') {
        setError(response);
        return;
      }

      setCategories(prevCategories =>
        prevCategories.map(cat =>
          cat.id === categoryId
            ? {
              ...cat,
              subcategories: mediaType === 'series'
                ? categoryType === "2"
                  ? (response.data.category || []).map((serie: SeriesCategory) => ({
                    id: serie.id,
                    name: serie.name,
                    type: serie.type,
                    logo: serie.thumb,
                    logo_desc: serie.logo,
                    back: serie.backlink,
                    actors: serie.actors,
                    date: serie.date,
                    rate: serie.rate,
                    category: serie.category,
                    description: serie.description
                  }))
                  : (response.data.category || []).map((serie: any) => ({
                    id: serie.id,
                    name: serie.name,
                    type: serie.type,
                    logo: serie.logo
                  }))
                : mediaType === 'movie'
                  ? categoryType === "1"
                    ? (response.data.movies || []).map((movie: Movie) => ({
                      id: movie.id,
                      name: movie.name,
                      type: movie.type,
                      logo: movie.thumb,
                      logo_desc: movie.logo,
                      back: movie.back,
                      actors: movie.actors,
                      language: movie.language,
                      sub: movie.sub,
                      link: movie.link,
                      trailer: movie.trailer,
                      date: movie.date,
                      date_aj: movie.date_aj,
                      duration: movie.duration,
                      rate: movie.rate,
                      category: movie.category,
                      desc: movie.desc
                    }))
                    : (response.data.category || []).map((subcat: any) => ({
                      id: subcat.id,
                      name: subcat.name,
                      type: subcat.type,
                      logo: subcat.logo
                    }))
                  : mediaType === 'radio'
                    ? (response.data.radio || []).map((station: RadioStation) => ({
                      id: station.id,
                      name: station.name,
                      logo: station.logo,
                      link: station.link,
                    }))
                    : mediaType === 'quran'
                      ? (response.data.coran || []).map((surah: any) => ({
                        id: surah.id,
                        name: surah.name,
                        logo: surah.logo,
                        link: surah.link,
                        dropback: surah.dropback
                      }))
                      : mediaType === 'tv'
                        ? (response.data.channels || []).map((channel: any) => ({
                          name: channel.name,
                          logo: channel.logo,
                          link: channel.ch
                        }))
                        : []
            }
            : cat
        )
      );

      // Mettre en cache les données uniquement pour radio, quran
      // (series et movie sont déjà gérés par useMediaCategories)
      if (mediaType === 'radio' || mediaType === 'quran') {
        const subcategories = mediaType === 'radio'
          ? (response.data.radio || []).map((station: RadioStation) => ({
            id: station.id,
            name: station.name,
            logo: station.logo,
            link: station.link,
          }))
          : mediaType === 'quran'
            ? (response.data.coran || []).map((surah: any) => ({
              id: surah.id,
              name: surah.name,
              logo: surah.logo,
              link: surah.link,
              dropback: surah.dropback
            }))
            : [];

        // Mettre à jour le cache avec les nouvelles données
        setLoadedCategories(prevCache => ({
          ...prevCache,
          [categoryId]: subcategories
        }));
      }

      if (mediaType === 'quran' && response.data?.coran) {
        const surahs = (response.data.coran || []).map((surah: any) => ({
          id: surah.id,
          name: surah.name,
          logo: surah.logo,
          link: surah.link,
          dropback: surah.dropback
        }));
        setAllSurahs(surahs);
      }

      setError(null);
    } catch (err) {
      setError(`Failed to load ${getErrorTypeText()}. Please try again later.`);
      console.error(`Error fetching ${getErrorTypeText()}:`, err);
    } finally {
      setLoading(false);
    }

    function getErrorTypeText() {
      switch (mediaType) {
        case 'series': return 'series';
        case 'radio': return 'radio stations';
        case 'quran': return 'Quran surahs';
        default: return 'subcategories';
      }
    }
  };

  // Fonction spécifique aux films
  const fetchMoviesForSubCategory = async (subCategoryId: string) => {
    if (mediaType !== 'movie') return;

    try {
      setLoading(true);

      // Save current state in history
      if (currentLevel === 'subcategory' && activeCategoryId) {
        setNavigationHistory([...navigationHistory, { level: `category_${activeCategoryId}`, index: subCategoryIndex }]);
      }
      setCurrentLevel('movie');

      const response = await api.fetchMoviesByMovies(subCategoryId);
      await fetchCategories(subCategoryId, 'movie');
      setActiveCategoryId(subCategoryId);

      if (typeof response === 'string') {
        setError(response);
        return;
      }

      setCategories(prevCategories =>
        prevCategories.map(cat =>
          cat.id === categories[expandedCategory].id
            ? {
              ...cat,
              subcategories: (response.data.movies || []).map((movie: any) => ({
                id: movie.id,
                name: movie.name,
                type: movie.type,
                actors: movie.actors,
                logo: movie.thumb,
                language: movie.language,
                sub: movie.sub,
                logo_desc: movie.logo,
                link: movie.link,
                trailer: movie.trailer,
                date: movie.date,
                back: movie.back,
                date_aj: movie.date_aj,
                duration: movie.duration,
                rate: movie.rate,
                category: movie.category,
                desc: movie.desc
              }))
            }
            : cat
        )
      );

      setError(null);
    } catch (err) {
      setError('Failed to load movies for this subcategory');
      console.error('Error fetching movies:', err);
    } finally {
      setLoading(false);
    }
  };

  // Gestion des touches clavier
  useEffect(() => {
    if (categories.length === 0) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Limitation de navigation - vérifier le cooldown pour les touches de navigation
      const now = Date.now();
      const isNavigationKey1 = ['ArrowUp', 'ArrowDown'].includes(e.key);
      const isNavigationKey2 = ['ArrowLeft', 'ArrowRight'].includes(e.key);

      if ((isNavigationKey1 && (now - lastNavigationTime.current) < navigationCooldown) || (isNavigationKey2 && (now - lastNavigationTime.current) < navigationCooldownRL)) {
        e.preventDefault();
        return; // Ignorer la pression si elle est trop rapide
      }

      if ((isNavigationKey1 && (selectedZone === "right")) || (isNavigationKey2 && (selectedZone === "right"))) {
        lastNavigationTime.current = now;
      }


      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Enter'].includes(e.key)) {
        e.preventDefault();
      }
      // Gestion de l'appui long sur Enter pour les favoris dans la zone droite
      if (e.key === 'Enter' && selectedZone === 'right') {
        console.log('Enter keydown detected, enterPressStart:', enterPressStartRef.current);
        if (!enterPressStartRef.current) {
          const timestamp = Date.now();
          console.log('Setting enterPressStart to:', timestamp);
          enterPressStartRef.current = timestamp;

          // Programmer l'alerte après 1 seconde de maintien
          setTimeout(() => {
            if (enterPressStartRef.current === timestamp) {
              console.log('Long press detected after 1 second');
              handleLongPressInRightZone();
              enterPressStartRef.current = null; // Éviter les doublons
            }
          }, 1000);
        }
        return;
      }

      // Gestion de l'appui long sur Enter pour les catégories dans la zone gauche
      if (e.key === 'Enter' && selectedZone === 'left') {
        console.log('Enter keydown detected for category, categoryEnterPressStart:', categoryEnterPressStartRef.current);
        if (!categoryEnterPressStartRef.current) {
          const timestamp = Date.now();
          console.log('Setting categoryEnterPressStart to:', timestamp);
          categoryEnterPressStartRef.current = timestamp;

          // Programmer l'alerte après 1 seconde de maintien
          setTimeout(() => {
            if (categoryEnterPressStartRef.current === timestamp) {
              console.log('Long press detected for category after 1 second');
              handleLongPressInLeftZone();
              categoryEnterPressStartRef.current = null; // Éviter les doublons
            }
          }, 1000);
        }
        return;
      }

      if (e.key === RETURN_KEY || e.keyCode === RETURN_KEY_CODE) {
        e.preventDefault();

        // Si searchQuery n'est pas vide, l'effacer en priorité
        if (searchQuery && searchQuery.trim() !== '') {
          setSearchQuery('');
          resetFilters();
          return;
        }

        // Si une catégorie est sélectionnée (filtrage par catégorie), la déselectionner
        if (selectedSubCategory) {
          resetFilters();
          return;
        }

        // Si nous sommes au niveau series ou subcategory et dans la zone droite, retourner à la zone gauche
        if (selectedZone === "right" && (currentLevel === 'series' || currentLevel === 'subcategory')) {
          setSelectedZone("left");
          return;
        }

        // If we're in the right zone and have navigation history
        if (selectedZone === "right" && navigationHistory.length > 0) {
          // Get the previous navigation level and index
          const prevNavigation = navigationHistory[navigationHistory.length - 1];
          const newHistory = [...navigationHistory];
          newHistory.pop();
          setNavigationHistory(newHistory);

          // Restore previous state
          if (prevNavigation.level === 'main') {
            // Return to main series/movies list
            setCurrentLevel('main');
            setActiveCategoryId(null);
            resetFilters();
            setCategoryIndex(prevNavigation.index); // Restaurer l'index de catégorie

            // Scroll to the selected category after a short delay to ensure the DOM is updated
            setTimeout(() => {
              if (categoriesContainerRef.current) {
                const container = categoriesContainerRef.current;
                const content = container.children[1];
                const selectedElement = content.children[0].children[prevNavigation.index] as HTMLElement;

                if (selectedElement) {
                  selectedElement.scrollIntoView({
                    behavior: "smooth",
                    block: "center"
                  });
                }
              }
            }, 100);
          } else if (prevNavigation.level.startsWith('category_')) {
            // Return to a specific category
            const categoryId = prevNavigation.level.split('_')[1];
            if (mediaType === 'series') {
              setCurrentLevel('series');
            } else if (mediaType === 'movie') {
              setCurrentLevel('subcategory');
            }
            setActiveCategoryId(categoryId);
            setSubCategoryIndex(prevNavigation.index); // Restaurer l'index de sous-catégorie

            // Reload data for this category and then scroll to the selected item
            fetchSubCategories(categoryId, categories.find(c => c.id === categoryId)?.type ?? '').then(() => {
              setTimeout(() => {
                if (subCategoriesContainerRef.current) {
                  const container = subCategoriesContainerRef.current;
                  const selectedElement = container.children[prevNavigation.index] as HTMLElement;

                  if (selectedElement) {
                    selectedElement.scrollIntoView({
                      behavior: "smooth",
                      block: "center"
                    });
                  }
                }
              }, 100);
            });
          } else if (prevNavigation.level.startsWith('series_') ||
            prevNavigation.level.startsWith('season1_') ||
            prevNavigation.level.startsWith('season2_')) {
            // Set the appropriate level
            if (prevNavigation.level.startsWith('series_')) {
              setCurrentLevel('season1');
            } else if (prevNavigation.level.startsWith('season1_')) {
              setCurrentLevel('season1');
            } else if (prevNavigation.level.startsWith('season2_')) {
              setCurrentLevel('season2');
            }

            // Get the ID from the level
            const id = prevNavigation.level.split('_')[1];
            setSubCategoryIndex(prevNavigation.index); // Restaurer l'index de sous-catégorie

            // Reload data and then scroll to the selected item
            navigateToSeriesDescription(id).then(() => {
              setTimeout(() => {
                if (subCategoriesContainerRef.current) {
                  const container = subCategoriesContainerRef.current;
                  const selectedElement = container.children[prevNavigation.index] as HTMLElement;

                  if (selectedElement) {
                    selectedElement.scrollIntoView({
                      behavior: "smooth",
                      block: "center"
                    });
                  }
                }
              }, 100);
            });
          }
          return;
        }

        // Default behavior if no history
        switch (selectedZone) {
          case "right": setSelectedZone("left"); return;
          case "search":
          case "categories":
            // Vérifier s'il y a des éléments dans la liste courante avant de naviguer à droite
            const selectedCategory = categories[categoryIndex];
            let hasItems = false;
            if (selectedCategory?.id === 'favorites') {
              hasItems = favorites.length > 0;
            } else if (selectedCategory?.id === 'still-watching') {
              hasItems = stillWatchingItems.length > 0;
            } else {
              hasItems = (selectedCategory?.subcategories && selectedCategory.subcategories.length > 0) ||
                (filteredSubcategories.length > 0);
            }

            if (hasItems) {
              setSelectedZone("right");
              // Désélectionner le bouton de recherche si on était sur la zone search
              if (selectedZone === "search") {
                setSearchFocused(false);
                searchInputRef.current?.blur();
                searchButtonRef.current?.blur();
              }
            }
            return;
          case "left":
            // Naviguer vers le menu avec le bon paramètre selon le mediaType
            let itemParam = "";
            switch (mediaType) {
              case 'tv': itemParam = 'live'; break;
              case 'movie': itemParam = 'vod'; break;
              case 'series': itemParam = 'series'; break;
              case 'radio': itemParam = 'radio'; break;
              case 'quran': itemParam = 'coran'; break;
              default: itemParam = 'live'; break;
            }
            navigate(`/?section=multipacks&item=${itemParam}`);
            return;
        }
      }

      if (selectedZone === "left") {
        if (e.key === "ArrowDown") {
          const newIndex = Math.min(categoryIndex + 1, categories.length - 1);
          setCategoryIndex(newIndex);
          setSubCategoryIndex(0);
        } else if (e.key === "ArrowUp") {
          const newIndex = Math.max(categoryIndex - 1, 0);
          setCategoryIndex(newIndex);
          setSubCategoryIndex(0);
        } else if (e.key === "ArrowRight") {
          // Vérifier s'il y a des éléments dans la liste courante avant de naviguer à droite
          const selectedCategory = categories[categoryIndex];
          if (selectedCategory) {
            let hasItems = false;

            if (selectedCategory.id === 'favorites') {
              hasItems = favorites.length > 0;
            } else if (selectedCategory.id === 'still-watching') {
              hasItems = stillWatchingItems.length > 0;
            } else {
              hasItems = (selectedCategory.subcategories && selectedCategory.subcategories.length > 0) ||
                (filteredSubcategories.length > 0);
            }

            // Ne naviguer à droite que s'il y a des éléments
            if (hasItems) {
              setSelectedZone("right");
            }
          }
        }
      } else if (selectedZone === "right") {
        // Déterminer la liste d'éléments actuelle en fonction du contexte
        let currentSubcategories;
        if (activeCategoryId === 'favorites') {
          currentSubcategories = favorites;
        } else if (activeCategoryId === 'still-watching') {
          currentSubcategories = stillWatchingItems;
        } else if (filteredSubcategories.length > 0) {
          currentSubcategories = filteredSubcategories;
        } else {
          currentSubcategories = categories[expandedCategory]?.subcategories || [];
        }

        const itemsPerRow = 5;
        const totalItems = currentSubcategories.length;
        const currentCol = subCategoryIndex % itemsPerRow;
        const currentRow = Math.floor(subCategoryIndex / itemsPerRow);
        const maxRow = Math.floor((totalItems - 1) / itemsPerRow);

        // Ne pas naviguer si il n'y a pas d'éléments
        if (totalItems === 0) {
          return;
        }

        if (e.key === "ArrowRight") {
          const isLastInRow = (subCategoryIndex % itemsPerRow) === itemsPerRow - 1;
          const isLastItem = subCategoryIndex === totalItems - 1;

          // Ne pas bouger si on est déjà sur le dernier élément
          if (isLastItem) {
            return;
          }

          if (isLastInRow) {
            if (currentRow < maxRow) {
              const nextRowStart = (currentRow + 1) * itemsPerRow;
              setSubCategoryIndex(nextRowStart);
            }
          } else {
            setSubCategoryIndex(subCategoryIndex + 1);
          }
        } else if (e.key === "ArrowLeft") {
          const isFirstInRow = subCategoryIndex % itemsPerRow === 0;
          const isFirstItem = subCategoryIndex === 0;

          if (isFirstInRow) {
            if (currentRow > 0) {
              const prevRowEnd = currentRow * itemsPerRow - 1;
              setSubCategoryIndex(prevRowEnd);
            } else {
              setSelectedZone("left");
            }
          } else if (!isFirstItem) {
            setSubCategoryIndex(subCategoryIndex - 1);
          }
        } else if (e.key === "ArrowDown") {
          const nextDirectIndex = subCategoryIndex + itemsPerRow;
          if (nextDirectIndex < totalItems) {
            setSubCategoryIndex(nextDirectIndex);
          } else {
            const nextRow = currentRow + 1;
            if (nextRow <= maxRow) {
              const nextIndex = nextRow * itemsPerRow + currentCol;
              const finalIndex = Math.min(nextIndex, totalItems - 1);
              // Ne naviguer que si on change vraiment d'index
              if (finalIndex !== subCategoryIndex) {
                setSubCategoryIndex(finalIndex);
              }
            }
          }
        } else if (e.key === "ArrowUp") {
          if (currentRow === 0) {
            // Pour les catégories favorites et still-watching, ne pas naviguer vers le search
            // car ces sections n'ont pas de bouton de recherche
            if (activeCategoryId === 'favorites' || activeCategoryId === 'still-watching') {
              // Rester dans la zone droite, ne pas naviguer vers le haut
              return;
            } else if (selectedSubCategory && mediaCategories.length > 0) {
              setSelectedZone("categories");
              const categoryIndex = mediaCategories.findIndex(cat => cat === selectedSubCategory);
              setCategoryBtnIndex(categoryIndex);
              categoryButtonsRef.current[categoryIndex]?.focus();
            } else {
              setSelectedZone("search");
              setSearchFocused(false);
              searchButtonRef.current?.focus();
            }
          } else {
            const prevRowIndex = subCategoryIndex - itemsPerRow;
            // Ne naviguer que si l'index de destination est valide
            if (prevRowIndex >= 0) {
              setSubCategoryIndex(prevRowIndex);
            }
          }
        }
      } else if (selectedZone === "search") {
        if (e.key === "ArrowDown") {
          // Vérifier s'il y a des éléments dans la liste courante avant de naviguer à droite
          let hasItems = false;
          // Si un filtrage est actif (searchQuery n'est pas vide), seuls les résultats filtrés comptent
          if (searchQuery && searchQuery.trim() !== '') {
            hasItems = filteredSubcategories.length > 0;
          } else {
            // Si aucun filtrage, vérifier les subcategories normales
            hasItems = (categories[expandedCategory]?.subcategories?.length || 0) > 0;
          }

          if (hasItems) {
            setSelectedZone("right");
            setSearchFocused(false);
            setSubCategoryIndex(0);
            searchInputRef.current?.blur();
            searchButtonRef.current?.blur();
          }
        } else if (e.key === "ArrowRight") {
          setSelectedZone("categories");
          setCategoryBtnIndex(0);
          categoryButtonsRef.current[0]?.focus();
        } else if (e.key === "Enter") {
          setSearchFocused(true);
        }
      } else if (selectedZone === "categories") {
        if (e.key === "ArrowLeft") {
          if (categoryBtnIndex === 0) {
            setSelectedZone("search");
            searchButtonRef.current?.focus();
          } else {
            const newIndex = categoryBtnIndex - 1;
            setCategoryBtnIndex(newIndex);
            categoryButtonsRef.current[newIndex]?.focus();
            categoryButtonsRef.current[newIndex]?.scrollIntoView({
              behavior: "smooth",
              block: "nearest",
              inline: "nearest",
            });
          }
        } else if (e.key === "ArrowRight") {
          if (categoryBtnIndex < mediaCategories.length - 1) {
            const newIndex = categoryBtnIndex + 1;
            setCategoryBtnIndex(newIndex);
            categoryButtonsRef.current[newIndex]?.focus();
            categoryButtonsRef.current[newIndex]?.scrollIntoView({
              behavior: "smooth",
              block: "nearest",
              inline: "nearest",
            });
          }
        } else if (e.key === "ArrowDown") {

          let hasItems = false;
          // Si un filtrage est actif (searchQuery n'est pas vide), seuls les résultats filtrés comptent
          if (searchQuery && searchQuery.trim() !== '') {
            hasItems = filteredSubcategories.length > 0;
          } else {
            // Si aucun filtrage, vérifier les subcategories normales
            hasItems = (categories[expandedCategory]?.subcategories?.length || 0) > 0;
          }
          if (hasItems) {
            setSelectedZone("right");
            setSubCategoryIndex(0);
            categoryButtonsRef.current[categoryBtnIndex]?.blur();
          }
        } else if (e.key === "Enter") {
          const selectedCategory = mediaCategories[categoryBtnIndex];
          filterByCategory(selectedCategory, categories[expandedCategory]?.subcategories || []);
          setSubCategoryIndex(0);
          setSelectedZone("right");
        }
      }
    };

    // Fonctions pour gérer les appuis longs
    const handleLongPressInRightZone = () => {
      let selectedItem;

      // Si nous sommes dans la section favoris
      if (activeCategoryId === 'favorites') {
        selectedItem = favorites[subCategoryIndex];
      } else if (activeCategoryId === 'still-watching') {
        // Si nous sommes dans la section still-watching
        selectedItem = stillWatchingItems[subCategoryIndex];
      } else {
        const currentSubcategories = filteredSubcategories.length > 0
          ? filteredSubcategories
          : categories[expandedCategory]?.subcategories || [];
        selectedItem = currentSubcategories[subCategoryIndex];
      }

      if (selectedItem && !selectedItem.isSpecial) {
        // Vérifier si l'élément est déjà dans les favoris
        const isAlreadyFavorite = isItemInFavorites(selectedItem);

        if (isAlreadyFavorite) {
          // Si déjà en favoris, proposer de le retirer
          const confirmMessage = t("removeFromFavorites").replace("{name}", selectedItem.name);
          if (window.confirm(confirmMessage)) {
            const success = removeFromFavorites(selectedItem);
            if (success) {
              alert(t("removedFromFavorites").replace("{name}", selectedItem.name));
            }
          }
        } else {
          // Si pas en favoris, proposer de l'ajouter
          const confirmMessage = t("addToFavorites").replace("{name}", selectedItem.name);
          if (window.confirm(confirmMessage)) {
            const success = addToFavorites(selectedItem);
            if (success) {
              alert(t("addedToFavorites").replace("{name}", selectedItem.name));
            } else {
              alert(t("alreadyInFavorites").replace("{name}", selectedItem.name));
            }
          }
        }
      }
    };

    const handleLongPressInLeftZone = () => {
      const selectedCategory = categories[categoryIndex];

      if (selectedCategory && !selectedCategory.isSpecial) {
        // Vérifier si la catégorie est déjà dans les favoris
        const isAlreadyFavorite = favoriteCategories.includes(selectedCategory.id);

        if (isAlreadyFavorite) {
          // Si déjà en favoris, proposer de la retirer
          const confirmMessage = t("removeCategoryFromFavorites").replace("{name}", selectedCategory.name);
          if (window.confirm(confirmMessage)) {
            const success = removeCategoryFromFavorites(selectedCategory.id);
            if (success) {
              alert(t("categoryRemovedFromFavorites").replace("{name}", selectedCategory.name));
            }
          }
        } else {
          // Si pas en favoris, proposer de l'ajouter
          const confirmMessage = t("addCategoryToFavorites").replace("{name}", selectedCategory.name);
          if (window.confirm(confirmMessage)) {
            const success = addCategoryToFavorites(selectedCategory.id);
            if (success) {
              alert(t("categoryAddedToFavorites").replace("{name}", selectedCategory.name));
            }
          }
        }
      }
    };
    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && enterPressStartRef.current && selectedZone === 'right') {
        const pressDuration = Date.now() - enterPressStartRef.current;
        console.log('Enter keyup detected, pressDuration:', pressDuration);

        // Si c'est un appui court (moins de 1 seconde), exécuter l'action normale
        if (pressDuration < 1000) {
          console.log('Short press detected, calling handleNormalEnterPress');
          handleNormalEnterPress();
        }

        enterPressStartRef.current = null;
        return;
      }

      // Gestion de l'appui court sur Enter pour les catégories (zone gauche)
      if (e.key === 'Enter' && categoryEnterPressStartRef.current && selectedZone === 'left') {
        const pressDuration = Date.now() - categoryEnterPressStartRef.current;
        console.log('Enter keyup detected for category, pressDuration:', pressDuration);

        // Si c'est un appui court (moins de 1 seconde), exécuter l'action normale
        if (pressDuration < 1000) {
          console.log('Short press detected for category');
          const selectedCat = categories[categoryIndex];
          resetFilters();
          setActiveCategoryId(selectedCat.id);
          setSubCategoryIndex(0);
          fetchSubCategories(selectedCat.id, selectedCat.type ?? '');

          // Vérifier si la liste courante sera vide avant de naviguer vers la droite
          let currentList = [];
          if (selectedCat.id === 'favorites') {
            currentList = favorites;
          } else if (selectedCat.id === 'still-watching') {
            currentList = stillWatchingItems;
          } else {
            // Pour les catégories normales, on suppose qu'elles ne sont pas vides
            currentList = [1]; // Liste non vide temporaire
          }

          // Ne naviguer vers la droite que si la liste n'est pas vide
          if (currentList.length > 0) {
            setSelectedZone("right");
          }
          setExpandedCategory(categoryIndex);
        }

        categoryEnterPressStartRef.current = null;
        return;
      }
    };

    const handleNormalEnterPress = () => {
      // Si nous sommes dans la section favoris, utiliser la liste des favoris
      if (activeCategoryId === 'favorites') {
        const selectedSub = favorites[subCategoryIndex];
        if (selectedSub) {
          if (mediaType === 'series') {
            navigateToSeriesDescription(selectedSub);
          } else if (mediaType === 'movie') {
            if (selectedSub?.type === "1") {
              fetchMoviesForSubCategory(selectedSub.id);
              setSubCategoryIndex(0);
            } else {
              navigate("/movie-description", {
                state: {
                  media: selectedSub,
                  navigationState: {
                    currentLevel,
                    activeCategoryId,
                    categoryIndex,
                    subCategoryIndex,
                    expandedCategory,
                    navigationHistory
                  }
                }
              });
              return;
            }
          } else if (mediaType === 'radio') {
            navigate("/player", {
              state: {
                media: selectedSub,
                type: 'radio',
                navigationState: {
                  currentLevel,
                  activeCategoryId,
                  categoryIndex,
                  subCategoryIndex,
                  expandedCategory,
                  navigationHistory
                }
              }
            });
          } else if (mediaType === 'quran') {
            navigate("/player", {
              state: {
                media: selectedSub,
                surahs: allSurahs,
                type: 'quran',
                navigationState: {
                  currentLevel,
                  activeCategoryId,
                  categoryIndex,
                  subCategoryIndex,
                  expandedCategory,
                  navigationHistory
                }
              }
            });
          } else if (mediaType === 'tv') {
            navigate("/player", {
              state: {
                media: selectedSub,
                type: 'tv',
                navigationState: {
                  currentLevel,
                  activeCategoryId,
                  categoryIndex,
                  subCategoryIndex,
                  expandedCategory,
                  navigationHistory
                }
              }
            });
          }
        }
        return;
      }

      // Si nous sommes dans la section still-watching, utiliser la liste stillWatchingItems
      if (activeCategoryId === 'still-watching') {
        const selectedSub = stillWatchingItems[subCategoryIndex];
        console.log("selectedSub", selectedSub)

        if (selectedSub) {
          if (selectedSub.mediaType === 'series') {
            navigateToSeriesDescription(selectedSub);

          } else if (selectedSub.mediaType === 'movie') {
            // Pour les films, naviguer vers la description de film
            navigate("/movie-description", {
              state: {
                media: selectedSub,
                startPosition: selectedSub.currentTime,
                restoreNavigation: true
              }
            });
          } else if (selectedSub.mediaType === 'radio') {
            navigate("/player", {
              state: {
                media: selectedSub,
                type: 'radio',
                startPosition: selectedSub.currentTime,
                navigationState: {
                  currentLevel,
                  activeCategoryId,
                  categoryIndex,
                  subCategoryIndex,
                  expandedCategory,
                  navigationHistory
                }
              }
            });
          } else if (selectedSub.mediaType === 'quran') {
            navigate("/player", {
              state: {
                media: selectedSub,
                type: 'quran',
                surahs: allSurahs, // Transmettre toutes les sourates
                startPosition: selectedSub.currentTime,
                navigationState: {
                  currentLevel,
                  activeCategoryId,
                  categoryIndex,
                  subCategoryIndex,
                  expandedCategory,
                  navigationHistory
                }
              }
            });
          } else if (selectedSub.mediaType === 'tv') {
            navigate("/player", {
              state: {
                media: selectedSub,
                type: 'tv',
                startPosition: selectedSub.currentTime,
                navigationState: {
                  currentLevel,
                  activeCategoryId,
                  categoryIndex,
                  subCategoryIndex,
                  expandedCategory,
                  navigationHistory
                }
              }
            });
          }
        }
        return;
      }

      const currentSubcategories = filteredSubcategories.length > 0
        ? filteredSubcategories
        : categories[expandedCategory]?.subcategories || [];

      const selectedSub = currentSubcategories[subCategoryIndex];

      console.log("currentSubcategories", currentSubcategories)
      console.log("selectedSub", selectedSub)

      if (mediaType === 'series') {
        navigateToSeriesDescription(selectedSub);
        return;
      } else if (mediaType === 'movie') {
        if (selectedSub?.type === "1") {
          fetchMoviesForSubCategory(selectedSub.id);
          setSubCategoryIndex(0);
        } else {
          navigate("/movie-description", {
            state: {
              media: selectedSub,
              navigationState: {
                currentLevel,
                activeCategoryId,
                categoryIndex,
                subCategoryIndex,
                expandedCategory,
                navigationHistory
              }
            }
          });
          return;
        }
      } else if (mediaType === 'radio') {
        // Jouer directement la station radio
        navigate("/player", {
          state: {
            media: selectedSub,
            type: 'radio',
            navigationState: {
              currentLevel,
              activeCategoryId,
              categoryIndex,
              subCategoryIndex,
              expandedCategory,
              navigationHistory
            }
          }
        });
        return;
      } else if (mediaType === 'quran') {
        // Lire la sura avec toutes les sourates de la catégorie
        navigate("/player", {
          state: {
            media: selectedSub,
            type: 'quran',
            surahs: allSurahs, // Transmettre toutes les sourates
            navigationState: {
              currentLevel,
              activeCategoryId,
              categoryIndex,
              subCategoryIndex,
              expandedCategory,
              navigationHistory
            }
          }
        });
        return;
      } else if (mediaType === 'tv') {
        // Lire la chaîne TV
        navigate("/player", {
          state: {
            media: selectedSub,
            type: 'tv',
            navigationState: {
              currentLevel,
              activeCategoryId,
              categoryIndex,
              subCategoryIndex,
              expandedCategory,
              navigationHistory
            }
          }
        });
        return;
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, [
    selectedZone,
    categoryIndex,
    subCategoryIndex,
    categories,
    expandedCategory,
    categoryBtnIndex,
    mediaCategories,
    mediaType,
    favorites,
    stillWatchingItems,
    currentLevel,
    activeCategoryId,
    navigationHistory,
    filteredSubcategories
  ]);

  useEffect(() => {
    if (selectedZone === "right" && subCategoriesContainerRef.current) {
      const container = subCategoriesContainerRef.current;
      const items = container.children;

      // Déterminer la liste d'éléments actuelle en fonction du contexte
      let currentSubcategories;
      if (activeCategoryId === 'favorites') {
        currentSubcategories = favorites;
      } else if (activeCategoryId === 'still-watching') {
        currentSubcategories = stillWatchingItems;
      } else if (filteredSubcategories.length > 0) {
        currentSubcategories = filteredSubcategories;
      } else {
        currentSubcategories = categories[expandedCategory]?.subcategories || [];
      }

      // Si l'élément n'existe pas encore, augmenter loadedItemsCount (seulement pour les catégories normales)
      if (subCategoryIndex >= items.length && !allItemsLoaded &&
        activeCategoryId !== 'favorites' && activeCategoryId !== 'still-watching') {
        const newCount = Math.min(
          loadedItemsCount + 30,
          currentSubcategories.length || 0
        );
        setLoadedItemsCount(newCount);
        return;
      }

      const selectedElement = items[subCategoryIndex] as HTMLElement;
      if (selectedElement) {
        const containerRect = container.getBoundingClientRect();
        const elementRect = selectedElement.getBoundingClientRect();

        if (elementRect.bottom > containerRect.bottom) {
          container.scrollTop += elementRect.bottom - containerRect.bottom + 20;
        } else if (elementRect.top < containerRect.top) {
          container.scrollTop -= containerRect.top - elementRect.top + 20;
        }
      }
    }
  }, [subCategoryIndex, selectedZone, loadedItemsCount, allItemsLoaded, activeCategoryId, favorites, stillWatchingItems, filteredSubcategories, categories, expandedCategory]);
  // Effets pour la gestion du focus et du scroll
  useEffect(() => {
    const searchInput = searchInputRef.current;
    if (searchInput) {
      if (selectedZone === "search" && searchFocused) {
        searchInput.focus();
      } else {
        searchInput.blur();
      }
    }
  }, [selectedZone, searchFocused]);

  // Ref pour suivre l'index précédent et détecter la direction du scroll
  const prevSubCategoryIndexRef = useRef(subCategoryIndex);

  useEffect(() => {
    if (selectedZone === "right" && subCategoriesContainerRef.current) {
      const container = subCategoriesContainerRef.current;
      const selectedElement = container.children[subCategoryIndex] as HTMLElement;

      if (selectedElement) {
        // Déterminer la direction du scroll
        const isScrollingDown = subCategoryIndex > prevSubCategoryIndexRef.current;
        const blockPosition = isScrollingDown ? "start" : "end";

        selectedElement.scrollIntoView({
          behavior: "smooth",
          block: blockPosition,
        });

        // Mettre à jour l'index précédent
        prevSubCategoryIndexRef.current = subCategoryIndex;
      }
    }

  }, [subCategoryIndex, selectedZone]);

  useEffect(() => {
    if (selectedZone === "left" && categoriesContainerRef.current) {
      const container = categoriesContainerRef.current;
      const content = container.children[1];
      const selectedElement = content.children[0].children[categoryIndex] as HTMLElement;

      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: "auto",
          block: "center",
        });
      }
    }
  }, [categoryIndex, selectedZone]);

  useEffect(() => {
    if (categories.length > 0 && !loading && !initialCategorySelected) {
      setInitialCategorySelected(true);
      setCategoryIndex(0);
      setExpandedCategory(0);
      setSubCategoryIndex(0);
      fetchSubCategories(categories[0].id, categories[0].type ?? '');
      setSelectedZone("left");
    }
  }, [categories, loading, initialCategorySelected]);

  // Ajoutez une nouvelle fonction pour naviguer vers la page de description de série
  const navigateToSeriesDescription = async (item: any) => {
    console.log("Initial API call for item:", item.id);
    try {
      // Premier appel API obligatoire
      const response = await api.fetchSeriesBySeries(item.id);

      if (typeof response === 'string') {
        setError(response);
        return;
      }

      const data = response.data?.category || [];
      console.log("API response data type:", data[0]?.type);

      // Cas 1: Si le résultat contient des éléments de type 1 (saisons)
      if (data.length > 0 && data[0].type === "1") {
        setLoadingSeries(true);
        console.log("Type 1 detected - navigating to SeriesDescription");
        const seasons = data.map((season: any) => ({
          id: season.id,
          name: season.name,
          type: season.type,
          logo: season.logo || season.thumb,
          description: season.description
        }));

        let episodes: Episode[] = [];
        if (seasons.length > 0) {
          const episodesResponse = await api.fetchEpisodes(seasons[0].id);
          if (typeof episodesResponse !== 'string') {
            episodes = (episodesResponse.data.episodes || []).map((episode: any) => ({
              id: episode.id,
              name: episode.name,
              type: episode.type,
              logo: episode.logo,
              thumb: episode.thumb,
              link: episode.link,
              duration: episode.duration,
              desc: episode.desc,
              dimension: episode.dimension ?? "",
              size: episode.size ?? "",
              actors: episode.actors ?? "",
              date: episode.date ?? ""
            }));
          }
        }

        return navigate("/series-description", {
          state: {
            series: item,
            seasons: seasons,
            episodes: episodes,
            currentSeasonId: seasons.length > 0 ? seasons[0].id : null,
            navigationState: {
              currentLevel,
              activeCategoryId,
              categoryIndex,
              subCategoryIndex,
              expandedCategory,
              navigationHistory
            }
          }
        });
      }

      // Cas 2: Si le résultat contient des éléments de type 2 (subcategories/series)
      if (data.length > 0 && data[0].type === "2") {
        console.log("Type 2 detected - updating current view");
        setLoading(true);

        // Mettre à jour l'affichage avec les nouveaux éléments
        setCategories(prevCategories =>
          prevCategories.map(cat =>
            cat.id === categories[expandedCategory].id
              ? {
                ...cat,
                subcategories: data.map((subItem: any) => ({
                  id: subItem.id,
                  name: subItem.name,
                  type: subItem.type,
                  logo: subItem.thumb || subItem.logo,
                  logo_desc: subItem.logo,
                  actors: subItem.actors,
                  date: subItem.date,
                  rate: subItem.rate,
                  category: subItem.category,
                  description: subItem.description
                }))
              }
              : cat
          )
        );

        // Mettre à jour l'état de navigation
        const newHistory = [...navigationHistory];
        if (currentLevel === 'main') {
          newHistory.push({ level: 'main', index: categoryIndex });
          setCurrentLevel('season1');
        } else if (currentLevel === 'series' && activeCategoryId) {
          newHistory.push({ level: `category_${activeCategoryId}`, index: subCategoryIndex });
          setCurrentLevel('season1');
        }
        setNavigationHistory(newHistory);

        setActiveCategoryId(item.id);
        return setSubCategoryIndex(0);
      }

      // Cas 3: Aucun type reconnu (erreur)
      throw new Error("Unknown item type in API response");

    } catch (err) {
      setError('No episodes');
      console.error('Error:', err);
    } finally {
      setLoading(false);
      setLoadingSeries(false);
    }
  };

  // Ajouter un effet pour vérifier si nous revenons de DetailMovie avec un état à restaurer
  useEffect(() => {
    if (location.state && location.state.restoreNavigation) {
      // Restaurer l'état de navigation
      const navState = location.state;

      // Marquer que nous avons déjà sélectionné une catégorie initiale
      setInitialCategorySelected(true);

      // Nettoyer l'état de location pour éviter de restaurer plusieurs fois
      navigate(location.pathname, { replace: true, state: {} });

      // Restaurer l'historique de navigation
      setNavigationHistory(navState.navigationHistory);

      // Restaurer les indices et niveaux
      setCategoryIndex(navState.categoryIndex);
      setExpandedCategory(navState.expandedCategory);

      // Stocker temporairement l'état de navigation pour le traiter quand les catégories seront chargées
      setPendingNavState(navState);
    } else {
      console.log("No navigation state to restore");
    }
  }, [location.state]);

  // Nouvel effet séparé pour traiter la restauration quand les catégories sont chargées
  useEffect(() => {
    if (categories.length > 0 && pendingNavState) {
      const navState = pendingNavState;
      console.log("Categories loaded, processing pending navigation state");

      // Nettoyer l'état temporaire
      setPendingNavState(null);

      // Fonction pour restaurer l'état en fonction du niveau
      const restoreState = async () => {
        try {
          // Si nous sommes au niveau 'movie', nous devons d'abord charger la catégorie parente
          if (navState.currentLevel === 'movie') {
            console.log("Restoring movie level");

            // Trouver la catégorie parente à partir de l'historique de navigation
            const parentCategoryEntry = navState.navigationHistory.find((entry: { level: string; }) =>
              entry.level.startsWith('category_')
            );

            if (parentCategoryEntry) {
              const parentCategoryId = parentCategoryEntry.level.split('_')[1];
              console.log("Found parent category ID:", parentCategoryId);

              // D'abord, charger la catégorie principale
              setCurrentLevel('main');

              console.log("Categories loaded, count:", categories.length);

              // Charger les sous-catégories de la catégorie parente
              console.log("Loading subcategories for parent category:", parentCategoryId);
              const parentCategory = categories.find(c => c.id === parentCategoryId);
              if (parentCategory) {
                await fetchSubCategories(parentCategoryId, parentCategory.type ?? '');
                console.log("Parent category subcategories loaded");

                // Maintenant, charger les films pour la sous-catégorie active
                console.log("Loading movies for subcategory:", navState.activeCategoryId);
                await fetchMoviesForSubCategory(navState.activeCategoryId);

                // Définir le niveau et l'ID de catégorie active
                setCurrentLevel(navState.currentLevel);
                setActiveCategoryId(navState.activeCategoryId);

                // Définir l'index de sous-catégorie et faire défiler vers l'élément
                setSubCategoryIndex(navState.subCategoryIndex);
                setSelectedZone("right");

                // Faire défiler vers l'élément sélectionné
                setTimeout(() => {
                  if (subCategoriesContainerRef.current) {
                    const container = subCategoriesContainerRef.current;
                    console.log("Container children count:", container.children.length);
                    console.log("Trying to scroll to index:", navState.subCategoryIndex);

                    const selectedElement = container.children[navState.subCategoryIndex] as HTMLElement;

                    if (selectedElement) {
                      console.log("Element found, scrolling into view");
                      selectedElement.scrollIntoView({
                        behavior: "smooth",
                        block: "center"
                      });
                    } else {
                      console.log("Element not found at index:", navState.subCategoryIndex);
                    }
                  }
                }, 300);
              } else {
                console.log("Parent category not found:", parentCategoryId);
              }
            } else {
              console.log("No parent category found in navigation history");
            }
          }
          // Si nous sommes au niveau 'subcategory', charger directement la catégorie
          else if (navState.currentLevel === 'subcategory') {
            console.log("Restoring subcategory level");

            // Attendre que les catégories principales soient chargées
            if (categories.length === 0) {
              await new Promise(resolve => {
                const checkCategories = () => {
                  if (categories.length > 0) {
                    resolve(true);
                  } else {
                    setTimeout(checkCategories, 100);
                  }
                };
                checkCategories();
              });
            }

            const selectedCategory = categories.find(c => c.id === navState.activeCategoryId);
            if (selectedCategory) {
              console.log("Found selected category:", selectedCategory.name);

              setCurrentLevel(navState.currentLevel);
              setActiveCategoryId(navState.activeCategoryId);

              // Charger les sous-catégories pour cette catégorie
              await fetchSubCategories(selectedCategory.id, selectedCategory.type ?? '');
              console.log("Subcategories loaded, setting index:", navState.subCategoryIndex);

              // Une fois les sous-catégories chargées, définir l'index
              setSubCategoryIndex(navState.subCategoryIndex);
              setSelectedZone("right");

              // Faire défiler vers l'élément sélectionné
              setTimeout(() => {
                if (subCategoriesContainerRef.current) {
                  const container = subCategoriesContainerRef.current;
                  const selectedElement = container.children[navState.subCategoryIndex] as HTMLElement;

                  if (selectedElement) {
                    selectedElement.scrollIntoView({
                      behavior: "smooth",
                      block: "center"
                    });
                  }
                }
              }, 300);
            } else {
              console.log("Selected category not found in categories array");
            }
          }
          else if (navState.currentLevel === 'series') {
            console.log("Restoring series level");

            // Attendre que les catégories principales soient chargées
            if (categories.length === 0) {
              console.log("Categories not loaded yet, waiting...");
              await new Promise(resolve => {
                const checkCategories = () => {
                  if (categories.length > 0) {
                    console.log("Categories loaded successfully, count:", categories.length);
                    resolve(true);
                  } else {
                    setTimeout(checkCategories, 100);
                  }
                };
                checkCategories();
              });
            }

            console.log("categories before selectedCategory", categories);
            console.log("Looking for activeCategoryId:", navState.activeCategoryId);
            console.log("Available category IDs:", categories.map(c => c.id));

            const selectedCategory = categories.find(c => c.id === navState.activeCategoryId);
            if (selectedCategory) {
              console.log("Found selected category:", selectedCategory.name);

              setCurrentLevel(navState.currentLevel);
              setActiveCategoryId(navState.activeCategoryId);

              // Charger les sous-catégories pour cette catégorie
              await fetchSubCategories(selectedCategory.id, selectedCategory.type ?? '');
              console.log("Series loaded, setting index:", navState.subCategoryIndex);

              // Une fois les séries chargées, définir l'index
              setSubCategoryIndex(navState.subCategoryIndex);
              setSelectedZone("right");

              // Faire défiler vers l'élément sélectionné
              setTimeout(() => {
                if (subCategoriesContainerRef.current) {
                  const container = subCategoriesContainerRef.current;
                  const selectedElement = container.children[navState.subCategoryIndex] as HTMLElement;

                  if (selectedElement) {
                    selectedElement.scrollIntoView({
                      behavior: "smooth",
                      block: "center"
                    });
                  }
                }
              }, 300);
            } else {
              console.log("Selected category NOT found!");
              console.log("navState.activeCategoryId:", navState.activeCategoryId);
              console.log("categories:", categories);
            }
          }
          // Dans la fonction restoreState, ajoutez cette condition après les conditions existantes
          else if (navState.currentLevel === 'season1' || navState.currentLevel === 'season2') {
            console.log("Restoring series level:", navState.currentLevel);

            // Attendre que les catégories principales soient chargées
            if (categories.length === 0) {
              await new Promise(resolve => {
                const checkCategories = () => {
                  if (categories.length > 0) {
                    resolve(true);
                  } else {
                    setTimeout(checkCategories, 100);
                  }
                };
                checkCategories();
              });
            }

            // Trouver la catégorie parente
            const parentCategoryEntry = navState.navigationHistory.find((entry: { level: string; }) =>
              entry.level.startsWith('category_') || entry.level === 'main'
            );

            if (parentCategoryEntry) {
              let parentCategoryId;
              if (parentCategoryEntry.level === 'main') {
                // Si le niveau parent est 'main', utiliser l'index pour trouver la catégorie
                parentCategoryId = categories[parentCategoryEntry.index]?.id;
              } else {
                // Sinon, extraire l'ID de la catégorie du niveau
                parentCategoryId = parentCategoryEntry.level.split('_')[1];
              }

              console.log("Found parent category ID:", parentCategoryId);

              // Charger les sous-catégories de la catégorie parente
              const parentCategory = categories.find(c => c.id === parentCategoryId);
              if (parentCategory) {
                await fetchSubCategories(parentCategoryId, parentCategory.type ?? '');
                console.log("Parent category subcategories loaded");

                // Maintenant, charger les séries pour la sous-catégorie active
                if (navState.activeCategoryId) {
                  // Pour les séries, nous devons charger les séries de cette sous-catégorie
                  const response = await api.fetchSeriesBySeries(navState.activeCategoryId);
                  if (typeof response === 'string') {
                    setError(response);
                    return;
                  }

                  // Mettre à jour les catégories avec les séries récupérées
                  setCategories(prevCategories =>
                    prevCategories.map(cat =>
                      cat.id === categories[expandedCategory].id
                        ? {
                          ...cat,
                          subcategories: (response.data.category || []).map((serie: any) => ({
                            id: serie.id,
                            name: serie.name,
                            type: serie.type,
                            logo: serie.thumb || serie.logo,
                            logo_desc: serie.logo,
                            actors: serie.actors,
                            date: serie.date,
                            rate: serie.rate,
                            category: serie.category,
                            description: serie.description
                          }))
                        }
                        : cat
                    )
                  );
                }
                // Définir le niveau et l'ID de catégorie active
                setCurrentLevel(navState.currentLevel);
                setActiveCategoryId(navState.activeCategoryId);

                // Définir l'index de sous-catégorie et faire défiler vers l'élément
                setSubCategoryIndex(navState.subCategoryIndex);
                setSelectedZone("right");

                // Faire défiler vers l'élément sélectionné
                setTimeout(() => {
                  if (subCategoriesContainerRef.current) {
                    const container = subCategoriesContainerRef.current;
                    const selectedElement = container.children[navState.subCategoryIndex] as HTMLElement;

                    if (selectedElement) {
                      selectedElement.scrollIntoView({
                        behavior: "smooth",
                        block: "center"
                      });
                    }
                  }
                }, 300);
              }
            }
          }
        } catch (error) {
          console.error("Error restoring state:", error);
        }
      };

      // Exécuter la fonction de restauration
      restoreState();
    }
  }, [categories.length, pendingNavState]);

  // Réorganiser les catégories quand les favoris changent OU quand les catégories sont chargées
  useEffect(() => {
    if (categories.length > 0 && favoriteCategories.length >= 0) {
      // Vérifier si les catégories ont besoin d'être réorganisées
      if (!areCategoriesOrdered(categories)) {
        const orderedCategories = orderCategoriesByFavorites(categories);
        setCategories(orderedCategories);
      } else {
        console.log("Categories already in correct order")
      }
    } else {
      console.log("Not reordering - categories or favorites not ready")
    }
  }, [favoriteCategories, categories.length]);

  if (loading && categories.length === 0) {
    return (
      <div className="flex h-screen bg-background text-white justify-center items-center">
        <div className="loader"></div>
      </div>
    );
  }



  if (error) {
    return (
      <div className="flex h-screen bg-background text-white justify-center items-center">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-2 bg-white/20 rounded-full hover:bg-white/30 transition-colors"
          >
            {t("tryAgain")}
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={`flex h-screen bg-background overflow-hidden ${loadingSeries ? 'blur-sm' : ''}`}>
        {/* Barre des catégories */}
        <div
          ref={categoriesContainerRef}
          className="w-1/4 bg-sidebar h-full flex flex-col"
        >
          <div className="h-36 flex-shrink-0 flex items-center justify-center">
            {showUpArrow && (
              <div className="absolute" style={{ width: "2%" }}>
                <img
                  src="/up-arrow-svgrepo-com.svg"
                  alt="Up"
                  className="w-full h-full"
                />
              </div>
            )}
          </div>

          <div className="flex-1 overflow-y-auto">
            <div className="justify-center flex flex-col">
              {categories.map((cat, i) => (
                <div
                  key={cat.id}
                  className={`text-4xl mt-4 transition rounded-lg px-8 py-4 flex items-center ${selectedZone === "left" && i === categoryIndex
                    ? "scale-125 text-white ml-10"
                    : expandedCategory === i
                      ? "scale-105 text-white bg-red-200/30"
                      : "text-gray-300"
                    } ${cat.isSpecial ? "italic" : ""}`}
                  onClick={() => {
                    setCategoryIndex(i);
                    resetFilters();
                    setExpandedCategory(i);
                    setSubCategoryIndex(0); // Réinitialiser l'index de sous-catégorie
                    fetchSubCategories(cat.id, cat.type ?? '');
                    setSelectedZone("right");
                  }}
                >
                  {/* Image de catégorie arrondie */}
                  {cat.logo && !cat.isSpecial && (
                    <div className="w-12 h-12 mr-4 flex-shrink-0">
                      <img
                        src={cat.logo.startsWith('http') ? cat.logo : `http://ll.ssdlist.xyz:2022${cat.logo}`}
                        alt={cat.name}
                        className="w-full h-full object-cover rounded-full"
                      />
                    </div>
                  )}

                  <div className="flex items-center">
                    {favoriteCategories.includes(cat.id) && !cat.isSpecial && "⭐ "}
                    {cat.name}
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="h-36 flex-shrink-0 flex items-center justify-center">
            {showDownArrow && (
              <div className="absolute" style={{ width: "2%" }}>
                <img
                  src="/down-arrow-svgrepo-com.svg"
                  alt="Down"
                  className="w-full h-full"
                />
              </div>
            )}
          </div>
        </div>

        {expandedCategory !== null &&
          activeCategoryId !== 'favorites' &&
          activeCategoryId !== 'still-watching' &&
          (
            <div className="wrap">
              <div className={`globalInputSearch ${selectedZone === "search" || selectedZone === "categories" ? "focused" : ""}`}>
                <input
                  type="search"
                  ref={searchInputRef}
                  className={`searchTerm ${selectedZone === "search"}`}
                  autoComplete="off"
                  placeholder="Search.."
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    filterBySearch(e.target.value, categories[expandedCategory]?.subcategories || []);
                  }}
                  onFocus={() => {
                    setSelectedZone("search");
                    setSearchFocused(true);
                  }}
                />
                <button
                  ref={searchButtonRef}
                  className={`searchbtn ${selectedZone === "search"}`}
                  onFocus={() => {
                    setSelectedZone("search");
                    setSearchFocused(false);
                  }}
                >
                  <i className="fa fa-search"></i>
                </button>

                {mediaType !== 'radio' &&
                  mediaCategories.length > 0 && (
                    <CategoriesFilter
                      mediaCategories={mediaCategories}
                      selectedZone={selectedZone}
                      categoryBtnIndex={categoryBtnIndex}
                      selectedSubCategory={selectedSubCategory}
                      onCategorySelect={(category) => {
                        filterByCategory(category, categories[expandedCategory]?.subcategories || []);
                        setSubCategoryIndex(0);
                        setSelectedZone("right");
                      }}
                      onCategoryFocus={(index: number) => {
                        setSelectedZone("categories");
                        setCategoryBtnIndex(index);
                      }}
                      categoryButtonsRef={categoryButtonsRef}
                    />
                  )}
              </div>
            </div>
          )}

        {/* Grille des sous-catégories avec images */}
        {expandedCategory !== null && (
          <div
            ref={subCategoriesContainerRef}
            className="flex-1 p-20 grid grid-cols-5 gap-8 space-x-2 mt-36 overflow-y-auto scroll-container"
          >
            {loading ? (
              Array.from({ length: 10 }).map((_, i) => (
                <div
                  key={i}
                  className="bg-gray-700 rounded-lg animate-pulse h-64 w-full"
                ></div>
              )))
              : activeCategoryId === 'favorites' ? (
                favorites.length === 0 ? (
                  <div className="col-span-5 flex justify-center items-center h-64">
                    <p className="text-3xl text-gray-300">{t("noFavoritesYet")}</p>
                  </div>
                ) : (favorites.map((fav: any, i: number) => (
                  <MovieThumbnail
                    key={`fav-${getMediaId(fav)}`}
                    sub={fav}
                    isSelected={selectedZone === "right" && subCategoryIndex === i}
                    isFavorite={true} // Toujours true dans la section favoris
                    mediaType={mediaType}
                  />
                ))
                )) : activeCategoryId === 'still-watching' ?
                (stillWatchingItems.length === 0 ? (
                  <div className="col-span-5 flex justify-center items-center h-64">
                    <p className="text-3xl text-gray-300">{t("noItemsInProgress")}</p>
                  </div>
                ) : (
                  stillWatchingItems.map((item: any, i: number) => (
                    <MovieThumbnail
                      key={getMediaId(item)}
                      sub={item}
                      isSelected={selectedZone === 'right' && subCategoryIndex === i}
                      progress={item.currentTime / item.duration}
                      isFavorite={isItemInFavorites(item)}
                      mediaType={item.mediaType || mediaType}
                    />
                  ))
                )
                ) : searchQuery && filteredSubcategories.length === 0 ? (
                  <div className="col-span-5 flex justify-center items-center h-64">
                    <div className="text-center">
                      <p className="text-3xl text-gray-300">{t("noResultsFound").replace("{query}", searchQuery)}</p>
                    </div>
                  </div>
                ) : (
                  (filteredSubcategories.length > 0 ? filteredSubcategories : categories[expandedCategory]?.subcategories)
                    .slice(0, loadedItemsCount)
                    .map((sub: any, i: number) => (<MovieThumbnail
                      key={getMediaId(sub)}
                      sub={sub}
                      isSelected={selectedZone === "right" && subCategoryIndex === i}
                      isFavorite={isItemInFavorites(sub)}
                      mediaType={mediaType}
                    />
                    ))
                )}
          </div>
        )}
      </div>
      {loadingSeries && (
        <div className="absolute inset-0 flex items-center justify-center z-50 bg-black/50">
          <div className="loader"></div>
        </div>
      )}
    </>
  );
};

export default MediaPage;