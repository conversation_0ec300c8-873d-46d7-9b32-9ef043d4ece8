import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import YouTube from 'react-youtube';
import { RETURN_KEY, RETURN_KEY_CODE } from '../utils/keysCode';

const TrailerPlayer = () => {
  const { state } = useLocation();
  const navigate = useNavigate();
  const trailerUrl = state?.trailerUrl;
  const trailerBack = state?.trailerBack;
  const [isLoading, setIsLoading] = useState(true);

  // Gestion navigation TV : retour avec touche Retour ou Escape
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === RETURN_KEY || e.keyCode === RETURN_KEY_CODE) {
        navigate(-1);
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [navigate]);

  if (!trailerUrl) {
    return <div>Aucun trailer</div>;
  }

  return (
    <div style={{ 
      width: '100vw', 
      height: '100vh', 
      background: 'black',
      position: 'relative'
    }}>
      {/* Loader pendant le chargement */}
      {isLoading && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 10,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          background: 'rgba(0,0,0,0.7)'
        }}>
          {trailerBack && (
            <img 
              src={trailerBack} 
              alt="background"
              className="absolute inset-0 w-full h-full object-cover opacity-40 pointer-events-none"

            />
          )}
          <div className="loader" style={{ zIndex: 2 }}></div>
        </div>
      )}

      <YouTube
        videoId={trailerUrl}
        opts={{
          width: '100%',
          height: '100%',
          playerVars: {
            autoplay: 1,
            controls: 0,
            modestbranding: 1,
            rel: 0,
            showinfo: 0,
            iv_load_policy: 3,
          },
        }}
        className="youtube-full-bg"
        onReady={event => {
          setIsLoading(false);
          event.target.seekTo(0);
        }}
        onError={() => {
          setIsLoading(false);
        }}
        onEnd={() => navigate(-1)}
      />
    </div>
  );
};

export default TrailerPlayer;