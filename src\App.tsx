import { HashRouter as Router, Routes, Route } from "react-router-dom";
import MenuLayout from "./pages/MenuLayout";
import MainLayout from "./components/MainLayout";
import "@fortawesome/fontawesome-free/css/all.min.css";
import MovieDescription from "./pages/DetailMovie";
import MediaPage from "./pages/MediaPage";
import Player from "./pages/Player";
import SeriesDescription from "./pages/SeriesDescription";
import LanguagePage from "./pages/LanguagePage";
import AboutPage from "./pages/AboutPage";
import EventsPage from "./pages/EventsPage";
import TrailerPlayer from "./pages/TrailerPlayer";
import LoginPage from "./pages/LoginPage";
import ProfilePage from "./pages/ProfilePage";
import WelcomePage from "./pages/WelcomePage";
import { useState } from "react";


function App() {
  const [showWelcome, setShowWelcome] = useState(true);
 
   const handleWelcomeComplete = () => {
     setShowWelcome(false);
     // Rediriger vers l'écran principal
   };
 
   if (showWelcome) {
     return <WelcomePage onComplete={handleWelcomeComplete} />;
   }


  return (
    <Router>
      <Routes>
        <Route path="/player" element={<Player />} />

        <Route element={<MainLayout />}>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/profile" element={<ProfilePage />} />
          <Route path="/language" element={<LanguagePage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/events" element={<EventsPage />} />
          <Route path="/" element={<MenuLayout />} />
          <Route path="/vod" element={<MediaPage mediaType="movie" />} />
          <Route path="/series" element={<MediaPage mediaType="series" />} />
          <Route path="/radio" element={<MediaPage mediaType="radio" />} />
          <Route path="/coran" element={<MediaPage mediaType="quran" />} />
          <Route path="/live" element={<MediaPage mediaType="tv" />} />
          <Route path="/movie-description" element={<MovieDescription />} />
          <Route path="/trailer" element={<TrailerPlayer />} />
          <Route path="/series-description" element={<SeriesDescription />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App;





