import { useLanguage } from './useLanguage';
import { translations, TranslationKey } from '../utils/translations';

export const useTranslation = () => {
  const { currentLanguage } = useLanguage();

  const t = (key: TranslationKey): string => {
    return translations[currentLanguage as keyof typeof translations]?.[key] || 
           translations.fr[key] || 
           key;
  };

  return { t, currentLanguage };
};
