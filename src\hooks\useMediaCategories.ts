import { useState } from 'react';
import { api } from '../utils/api';
import { Movie } from '../types/movies';
import { SeriesCategory } from '../types/series';

const useMediaCategories = () => {
  const [categoriesCache, setCategoriesCache] = useState<Record<string, string[]>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = async (id: string, mediaType: 'movie' | 'series') => {
   // console.log("calling fetchCategories in useMedia with id",id, "and mediaType", mediaType );
    // Si les catégories sont déjà en cache, on ne refait pas la requête
    if (categoriesCache[id]) {
     // console.log('Categories already in cache:', categoriesCache[id]);
      return categoriesCache[id];
    }

    try {
      setLoading(true);
      setError(null);
      let response;
      
      if (mediaType === 'movie') {
        response = await api.fetchMoviesByMovies(id);
      } else {
        response = await api.fetchSeriesBySeries(id);
       // console.log('Call api fetchSeriesBySeries with id :', id);
      }

      if (typeof response === 'string') {
        setError(response);
        return;
      }

      // Extraction des catégories en fonction du type de média
      let uniqueCategories: string[] = [];

      if (mediaType === 'movie') {
        const allCategories = response.data.movies.flatMap((movie: Movie) => 
          movie.category?.split(',').map(cat => cat.trim()) || []
        );
        uniqueCategories = [...new Set(allCategories)].filter(Boolean);
      } else {
        const allCategories = (response.data.category as SeriesCategory[]).flatMap((series: SeriesCategory) =>
          series.category?.split(',').map(cat => cat.trim()) || []
        );
        uniqueCategories = [...new Set(allCategories)].filter(Boolean);
       // console.log('Unique categories for series:', uniqueCategories);
      }

      // Mise à jour du cache
      setCategoriesCache(prev => ({
        ...prev,
        [id]: uniqueCategories
      }));

      //console.log('Updated categories cache:', categoriesCache);
      
    } catch (err) {
      setError(`Failed to load ${mediaType} categories`);
      console.error(`Error fetching ${mediaType} categories:`, err);
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour récupérer les catégories pour une ID spécifique
  const getCategories = (id: string) => {
    //console.log('Getting categories for ID:', id);
    return categoriesCache[id] || [];
  };

  return { 
    getCategories, 
    loading, 
    error, 
    fetchCategories 
  };
};

export default useMediaCategories;