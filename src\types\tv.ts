
export interface TVCategory {
  id: string;
  name: string;
  logo: string;
}

export interface TVChannel {
  ch: string;
  name: string;
  logo: string;
  // EPG Data (Base64 encoded)
  current?: string;
  desc_current?: string;
  next?: string;
  desc_next?: string;
  start_current?: string;
  end_current?: string;
  start_next?: string;
  end_next?: string;
}

export interface EPGProgram {
  title: string;
  description: string;
  startTime: number;
  endTime: number;
}


export interface TVChannelsApiResponse {
  channels: TVChannel[];
}