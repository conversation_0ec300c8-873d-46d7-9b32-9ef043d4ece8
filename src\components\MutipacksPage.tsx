import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useTranslation } from "../hooks/useTranslation";
import { storage } from "../utils/storage";


const MultipacksPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Récupérer le code de la session
  const session = storage.getSession();
  const userCode = session?.pin || "";

  // Définir tous les éléments du menu
  const allMenuItems = [
    { id: 0, label: t("live"), icon: "/live.svg", action: () => navigate("/live") },
    { id: 1, label: t("vod"), icon: "/vod.svg", action: () => navigate("/vod") },
    { id: 2, label: t("series"), icon: "/series.svg", action: () => navigate("/series") },
    { id: 3, label: t("radio"), icon: "/radio.svg", action: () => navigate("/radio") },
    { id: 4, label: t("coran"), icon: "/coran.svg", action: () => navigate("/coran") },
    { id: 5, label: t("event"), icon: "/event.svg", action: () => navigate("/events") },

  ];

  // Filtrer les éléments selon le code utilisateur
  const menuItems = userCode === "0000" 
    ? allMenuItems.filter(item => item.id === 3 || item.id === 4) // Seulement radio et coran
    : allMenuItems;

  const [selectedIndex, setSelectedIndex] = useState(() => {
    // Initialiser l'index basé sur le paramètre URL
    const item = searchParams.get('item');
    let targetId = -1;
    
    switch(item) {
      case 'live': targetId = 0; break;
      case 'vod': targetId = 1; break;
      case 'series': targetId = 2; break;
      case 'radio': targetId = 3; break;
      case 'coran': targetId = 4; break;
      case 'events': targetId = 5; break;
      default: return 0;
    }
    
    // Trouver l'index dans menuItems filtrés
    const foundIndex = menuItems.findIndex(menuItem => menuItem.id === targetId);
    return foundIndex !== -1 ? foundIndex : 0;
  });

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "ArrowRight") {
      setSelectedIndex((prev) => (prev + 1) % menuItems.length); // Aller à droite
    } else if (event.key === "ArrowLeft") {
      setSelectedIndex((prev) => (prev - 1 + menuItems.length) % menuItems.length); // Aller à gauche
    } else if (event.key === "Enter") {
      menuItems[selectedIndex].action(); // Exécuter l'action de l'élément sélectionné
    }
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectedIndex]);

  return (
    <div className="flex h-screen bg-background text-white">
      <div className="flex-1 flex flex-row justify-center items-center space-x-16">
        {menuItems.map((item, index) => (
          <div
            key={item.id}
            className={`flex flex-col items-center transition-transform duration-200 ease-in-out ${selectedIndex === index
                ? "scale-110"
                : ""
              }`}
            style={{ transformOrigin: 'center' }}
          >
            <div className="relative mb-4">
              <div
                className={`${selectedIndex === index
                    ? "bg-gray-700 rounded-full absolute inset-0 w-32 h-32 -m-4"
                    : ""
                  }`}
              />
              <img src={item.icon} alt={`${item.label} Icon`} className="w-24 h-24 relative z-10" />
            </div>
            <button className="text-5xl mt-2">{item.label}</button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MultipacksPage;