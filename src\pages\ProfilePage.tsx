import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "../hooks/useTranslation";
import { storage } from "../utils/storage";
import { RETURN_KEY, RETURN_KEY_CODE } from "../utils/keysCode";

const ProfilePage = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [sessionData, setSessionData] = useState<any>(null);
  const [expiryInfo, setExpiryInfo] = useState<any>(null);

  useEffect(() => {
    // Récupérer les données de session
    const session = storage.getSession();
    console.log("session", session);
    const expiry = storage.getSessionExpiry();
    
    setSessionData(session);
    setExpiryInfo(expiry);
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.keyCode === 10009 || e.key === RETURN_KEY || e.keyCode === RETURN_KEY_CODE) {
        e.preventDefault();
        navigate("/?section=settings&item=profile");
        return;
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [navigate]);

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return t("notAvailable") || "N/A";
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + " " + date.toLocaleTimeString();
    } catch (error) {
      return dateString;
    }
  };

  const getStatusColor = () => {
    if (!expiryInfo) return "text-gray-400";
    if (expiryInfo.isExpired) return "text-red-400";
    if (expiryInfo.daysRemaining && expiryInfo.daysRemaining < 30) return "text-yellow-400";
    return "text-green-400";
  };

  const getStatusText = () => {
    if (!expiryInfo) return t("unknown") || "Unknown";
    if (expiryInfo.isExpired) return t("expired") || "Expired";
    if (expiryInfo.daysRemaining !== null) {
      return `${expiryInfo.daysRemaining} ${t("daysRemaining") || "days remaining"}`;
    }
    return t("active") || "Active";
  };

  if (!sessionData) {
    return (
      <div className="flex h-screen bg-background text-white items-center justify-center">
        <div className="text-6xl text-gray-400">
          {t("noSessionData") || "No session data available"}
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-background text-white">
      <div className="flex-1 flex flex-col justify-center items-center space-y-12 px-24">
        {/* Titre */}
        <div className="text-8xl font-bold text-center mb-8">
          {t("profile")}
        </div>

        {/* Informations de session */}
        <div className="w-full max-w-4xl space-y-8">

          {/* Date de début */}
          <div className="flex justify-between items-center border-b border-gray-600 pb-4">
            <span className="text-4xl text-gray-300">{t("startDate") || "Start Date"}:</span>
            <span className="text-4xl">{formatDate(sessionData.start)}</span>
          </div>

          {/* Date de fin */}
          <div className="flex justify-between items-center border-b border-gray-600 pb-4">
            <span className="text-4xl text-gray-300">{t("endDate") || "End Date"}:</span>
            <span className="text-4xl">{formatDate(sessionData.end)}</span>
          </div>

          {/* Statut */}
          <div className="flex justify-between items-center border-b border-gray-600 pb-4">
            <span className="text-4xl text-gray-300">{t("status")}:</span>
            <span className={`text-4xl font-bold ${getStatusColor()}`}>
              {getStatusText()}
            </span>
          </div>

          {/* Date de connexion */}
          <div className="flex justify-between items-center">
            <span className="text-4xl text-gray-300">{t("loginTime") || "Login Time"}:</span>
            <span className="text-4xl">
              {new Date(sessionData.timestamp).toLocaleDateString()} {new Date(sessionData.timestamp).toLocaleTimeString()}
            </span>
          </div>
        </div>

        {/* Instructions */}
        <div className="text-3xl text-gray-500 text-center mt-12">
          {t("pressBackToReturn") || "Press Back to return"}
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
