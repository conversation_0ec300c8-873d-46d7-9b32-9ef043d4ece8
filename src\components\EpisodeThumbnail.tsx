import { useEffect, useState } from "react";
import { getCachedData } from "../utils/tizenStorage";
import React from "react";

export const EpisodeThumbnail = React.memo(({
    episode,
    isSelected,
    onClick
}: {
    episode: any,
    isSelected: boolean,
    onClick: () => void
}) => {
    const [progressPercent, setProgressPercent] = useState<number | null>(null);

    useEffect(() => {
        const checkProgress = async () => {
            if (!episode || !episode.link) return;
            try {
                const savedPosition = await getCachedData<{
                    position: number;
                    timestamp: number;
                    duration: number;
                }>(`movie_position_${episode.link}`);

                if (savedPosition && savedPosition.position > 0 && savedPosition.duration > 0) {
                    console.log("                if (savedPosition && savedPosition.position > 0 && savedPosition.duration > 0) {")
                    const percent = Math.min(100, Math.round((savedPosition.position / savedPosition.duration) * 100));
                    console.log("percent", percent)
                    if (percent < 95) {
                        setProgressPercent(percent);
                    }
                }
            } catch (error) {
                console.error('Error checking progress for episode:', error);
            }
        };

        checkProgress();
    }, [episode.link]);


    return (
        <div
            className={`relative rounded overflow-hidden cursor-pointer ${isSelected ? 'ring-4 ring-red-600' : ''}`}
            onClick={onClick}
        >
            <img
                src={episode.logo}
                alt={episode.name}
                className="w-full h-52 object-cover"
            />

            {/* Conteneur du dégradé et du texte */}
            <div className="absolute bottom-0 left-0 right-0">
                {/* Barre de progression - positionnée en bas avant le dégradé */}
                {progressPercent !== null && (
                    <div className="w-full h-3 bg-gray-800 overflow-hidden">
                        <div
                            className="h-full bg-red-600"
                            style={{ width: `${progressPercent}%` }}
                        ></div>
                    </div>
                )}

                <div className="p-2 bg-gray-800">
                    <p className="text-lg font-semibold">{episode.name}</p>
                    {episode.duration && (
                        <p className="text-sm text-gray-300">
                            {episode.duration ? episode.duration.replace('http://ll.ssdlist.xyz:2022', '') : ''}
                        </p>
                    )}
                </div>
            </div>
        </div>
    );
});

