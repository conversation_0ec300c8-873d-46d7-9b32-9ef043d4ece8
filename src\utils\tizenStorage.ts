import { encrypt, decrypt } from "./storage";

const CACHE_FILE_NAME = 'api_cache_v2.json';
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 heures
const SAVE_DEBOUNCE_TIME = 5000; // 5 secondes

declare const tizen: any;

declare global {
  interface Window {
    tizen?: any;
  }
}

interface CacheEntry {
  data: any;
  timestamp: number;
}

let apiCache: Record<string, CacheEntry> = {};
let cacheLoaded = false;
let savePending = false;
let saveTimer: any = null;

export const isTizenAvailable = (): boolean => {
  try {
    return (
      typeof window !== 'undefined' &&
      typeof window.tizen !== 'undefined' &&
      typeof window.tizen.filesystem !== 'undefined' &&
      typeof window.tizen.filesystem.resolve === 'function'
    );
  } catch (e) {
    console.error('Tizen detection error:', e);
    return false;
  }
};

export const loadCacheFromTizen = async (): Promise<void> => {
  if (!isTizenAvailable()) {
   // console.log('Tizen filesystem not available - skipping cache load');
    return;
  }

  try {
    let documentsDir;
    try {
      documentsDir = tizen.filesystem.resolve('documents');
    } catch (e) {
      console.log('Could not resolve documents directory:', e);
      // Essayer de créer le répertoire s'il n'existe pas
      try {
        documentsDir = tizen.filesystem.createDirectory('documents');
      } catch (createError) {
        console.error('Failed to create documents directory:', createError);
        return;
      }
    }

    let cacheFile;
    try {
      cacheFile = documentsDir.resolve(CACHE_FILE_NAME);
      if (!cacheFile.isFile()) {
        console.log('Cache file does not exist yet');
        return;
      }
    } catch (e) {
      console.log('Cache file not found, will be created later');
      return;
    }

    const fileStream = cacheFile.openStream('r');
    const fileContent = fileStream.read(cacheFile.fileSize);
    fileStream.close();

    try {
      const decryptedContent = decrypt(fileContent);
      const parsedCache = JSON.parse(decryptedContent);
      
      const now = Date.now();
      apiCache = Object.fromEntries(
        Object.entries(parsedCache)
          .filter(([_, entry]) => 
            now - (entry as CacheEntry).timestamp <= CACHE_EXPIRY
          )
      ) as Record<string, CacheEntry>;
    } catch (parseError) {
      console.error('Cache parse error, resetting cache', parseError);
      apiCache = {};
    }
  } catch (error) {
    console.error('Error loading Tizen cache:', error);
    apiCache = {};
  } finally {
    cacheLoaded = true;
  }
};

const saveCacheToTizen = async (): Promise<void> => {
  if (!isTizenAvailable()) {
   // console.log('Tizen filesystem not available - skipping cache save');
    return;
  }

  if (!cacheLoaded || savePending) return;
  savePending = true;
  clearTimeout(saveTimer);

  try {
    const documentsDir = tizen.filesystem.resolve('documents');
    let cacheFile;

    try {
      cacheFile = documentsDir.resolve(CACHE_FILE_NAME);
    } catch (e) {
      cacheFile = documentsDir.createFile(CACHE_FILE_NAME);
    }

    const cacheContent = JSON.stringify(apiCache);
    const encryptedContent = encrypt(cacheContent);

    const fileStream = cacheFile.openStream('w');
    fileStream.write(encryptedContent);
    fileStream.close();
  } catch (error) {
    console.error('Error saving cache:', error);
  } finally {
    savePending = false;
  }
};

const debouncedSaveCache = () => {
  clearTimeout(saveTimer);
  saveTimer = setTimeout(() => {
    saveCacheToTizen().catch(console.error);
  }, SAVE_DEBOUNCE_TIME);
};

export const getCachedData = async <T>(cacheKey: string): Promise<T | null> => {
  if (!cacheLoaded) {
    await loadCacheFromTizen();
  }

  const cacheEntry = apiCache[cacheKey];
  if (!cacheEntry) return null;

  if (Date.now() - cacheEntry.timestamp > CACHE_EXPIRY) {
    delete apiCache[cacheKey];
    debouncedSaveCache();
    return null;
  }

  return cacheEntry.data as T;
};

export const setCachedData = async <T>(cacheKey: string, data: T): Promise<void> => {
  if (!cacheLoaded) {
    await loadCacheFromTizen();
  }

  apiCache[cacheKey] = {
    data,
    timestamp: Date.now()
  };

  debouncedSaveCache();
};

export const clearCache = async (): Promise<void> => {
  apiCache = {};
  await saveCacheToTizen();
};

// Fonction utilitaire pour vérifier le cache
export async function checkCacheValidity(cacheKeys: string[]): Promise<boolean> {
  try {
    const now = Date.now();
    
    for (const key of cacheKeys) {
      const cachedData = await getCachedData<any>(key);
      if (!cachedData) return false;
      
      // Vérifier si le cache est expiré (24h dans votre cas)
      if (now - cachedData.timestamp > CACHE_EXPIRY) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Erreur lors de la vérification du cache:', error);
    return false;
  }
}
