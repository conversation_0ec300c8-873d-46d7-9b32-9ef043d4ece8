import { useState, useEffect, RefObject } from 'react';

export const useScrollArrows = (containerRef: RefObject<HTMLElement | null>, dependencies: any[] = []) => {
  const [showUpArrow, setShowUpArrow] = useState(false);
  const [showDownArrow, setShowDownArrow] = useState(false);

  // Fonction pour vérifier si les flèches de scroll sont nécessaires
  const checkScrollArrows = () => {
    if (containerRef.current) {
      const container = containerRef.current;
      const content = container.children[1] as HTMLElement; // Le div avec flex-1 overflow-y-auto
      
      if (content) {
        const hasVerticalScroll = content.scrollHeight > content.clientHeight;
        const isAtTop = content.scrollTop <= 5; // Petit seuil pour éviter les micro-mouvements
        const isAtBottom = content.scrollTop >= content.scrollHeight - content.clientHeight - 5;
        
        setShowUpArrow(hasVerticalScroll && !isAtTop);
        setShowDownArrow(hasVerticalScroll && !isAtBottom);
      } else {
        setShowUpArrow(false);
        setShowDownArrow(false);
      }
    }
  };

  // useEffect pour gérer la visibilité des flèches de scroll
  useEffect(() => {
    checkScrollArrows();
  }, dependencies);

  // useEffect pour ajouter l'événement de scroll
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      const content = container.children[1] as HTMLElement;
      if (content) {
        const handleScroll = () => {
          checkScrollArrows();
        };
        content.addEventListener('scroll', handleScroll);
        return () => content.removeEventListener('scroll', handleScroll);
      }
    }
  }, [containerRef.current]);

  return {
    showUpArrow,
    showDownArrow,
    checkScrollArrows
  };
};
