import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "../hooks/useTranslation";
import { RETURN_KEY, RETURN_KEY_CODE } from "../utils/keysCode";

const AboutPage = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === RETURN_KEY || event.keyCode === RETURN_KEY_CODE) {
      navigate("/?section=settings&item=about");
    }
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  return (
    <div className="flex h-screen bg-background text-white">
      <div className="flex-1 flex flex-col justify-center items-center text-center">
        <div className="max-w-4xl mx-auto space-y-8">          
          <h1 className="text-6xl font-bold mb-8">Smart+ TV</h1>
          
          <div className="space-y-4 text-2xl text-gray-300">
            <p>{t("version")} 1.0.0</p>
            <p>{t("mediaStreamingApp")}</p>
            <p>{t("developedFor")}</p>
          </div>
          
          <div className="space-y-6 text-xl text-gray-400 mt-12">
            <p>{t("features")}</p>
            <ul className="space-y-2">
              <li>{t("featuresRadio")}</li>
              <li>{t("featuresQuran")}</li>
            </ul>
          </div>

        </div>
      </div>
    </div>
  );
};

export default AboutPage;
