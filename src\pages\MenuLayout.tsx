import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import SettingsPage from "../components/SettingsPage";
import MultipacksPage from "../components/MutipacksPage";
import { useTranslation } from "../hooks/useTranslation";
//import { useApiPreloader } from "../hooks/useApiPreloader";


const MenuLayout = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedSectionIndex, setSelectedSectionIndex] = useState(() => {
    // Initialiser l'index basé sur le paramètre URL
    const section = searchParams.get('section');
    return section === 'settings' ? 1 : 0;
  });
  const { t } = useTranslation();

    //const { error  } = useApiPreloader();
  //console.log("error",error )

  const sections = [t("multipacks"), t("settings")];

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === "ArrowUp") {
      const newIndex = (selectedSectionIndex - 1 + sections.length) % sections.length;
      setSelectedSectionIndex(newIndex);
      setSearchParams({ section: newIndex === 1 ? 'settings' : 'multipacks' });
    } else if (e.key === "ArrowDown") {
      const newIndex = (selectedSectionIndex + 1) % sections.length;
      setSelectedSectionIndex(newIndex);
      setSearchParams({ section: newIndex === 1 ? 'settings' : 'multipacks' });
    }
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [selectedSectionIndex, setSearchParams]);

  return (
    <div className="flex h-screen text-white bg-background">
      <div className="w-1/4 bg-sidebar flex flex-col items-center justify-center gap-10">
        {sections.map((section, index) => (
          <div
            key={index}
            className={`px-4 py-2 ${
              selectedSectionIndex === index ? "text-5xl" : "text-3xl"
            }`}
          >
            {section}
          </div>
        ))}
      </div>

      <div className="flex-1">
        {selectedSectionIndex === 0 ? <MultipacksPage /> : <SettingsPage />}
      </div>
    </div>
  );
};

export default MenuLayout;
