import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import SettingsPage from "../components/SettingsPage";
import MultipacksPage from "../components/MutipacksPage";
import { useTranslation } from "../hooks/useTranslation";
import { storage } from "../utils/storage";
//import { useApiPreloader } from "../hooks/useApiPreloader";


const MenuLayout = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { t } = useTranslation();

  // Vérifier si l'utilisateur est connecté
  const [isUserLoggedIn, setIsUserLoggedIn] = useState(() => storage.getSession() !== null);

  // Définir les sections en fonction de l'état de connexion
  const sections = isUserLoggedIn ? [t("multipacks"), t("settings")] : [t("settings")];

  const [selectedSectionIndex, setSelectedSectionIndex] = useState(() => {
    // Initialiser l'index basé sur le paramètre URL
    const section = searchParams.get('section');
    if (isUserLoggedIn) {
      return section === 'settings' ? 1 : 0;
    } else {
      // Si pas connecté, il n'y a que settings (index 0)
      return 0;
    }
  });

  // Surveiller les changements de session
  useEffect(() => {
    const checkSession = () => {
      const sessionExists = storage.getSession() !== null;
      if (sessionExists !== isUserLoggedIn) {
        setIsUserLoggedIn(sessionExists);
        // Si l'utilisateur se déconnecte, forcer la sélection de settings
        if (!sessionExists) {
          setSelectedSectionIndex(0);
          setSearchParams({ section: 'settings' });
        }
      }
    };

    // Vérifier immédiatement
    checkSession();

    // Vérifier périodiquement (au cas où la session change)
    const interval = setInterval(checkSession, 1000);

    return () => clearInterval(interval);
  }, [isUserLoggedIn, setSearchParams]);

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === "ArrowUp") {
      const newIndex = (selectedSectionIndex - 1 + sections.length) % sections.length;
      setSelectedSectionIndex(newIndex);
      if (isUserLoggedIn) {
        setSearchParams({ section: newIndex === 1 ? 'settings' : 'multipacks' });
      } else {
        setSearchParams({ section: 'settings' });
      }
    } else if (e.key === "ArrowDown") {
      const newIndex = (selectedSectionIndex + 1) % sections.length;
      setSelectedSectionIndex(newIndex);
      if (isUserLoggedIn) {
        setSearchParams({ section: newIndex === 1 ? 'settings' : 'multipacks' });
      } else {
        setSearchParams({ section: 'settings' });
      }
    }
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [selectedSectionIndex, setSearchParams]);

  return (
    <div className="flex h-screen text-white bg-background">
      <div className="w-1/4 bg-sidebar flex flex-col items-center justify-center gap-10">
        {sections.map((section, index) => (
          <div
            key={index}
            className={`px-4 py-2 ${
              selectedSectionIndex === index ? "text-5xl" : "text-3xl"
            }`}
          >
            {section}
          </div>
        ))}
      </div>

      <div className="flex-1">
        {isUserLoggedIn ?
          (selectedSectionIndex === 0 ? <MultipacksPage /> : <SettingsPage />) :
          <SettingsPage />
        }
      </div>
    </div>
  );
};

export default MenuLayout;
