export interface Event {
  id: string;
  name: string;
  logoL: string;
  logoR: string;
  competition: string;
  start: string;
  duration: string;
  logo: string | null;
}

export interface EventsApiResponse {
  events: Event[];
}

export interface EventChannel {
  id: string;
  name: string;
  logo: string;
  link: string;
}

export interface EventChannelsApiResponse {
  channels: EventChannel[];
}
