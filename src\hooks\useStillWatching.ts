import { useState, useEffect } from 'react';

export const useStillWatching = (mediaType: string) => {
  const [stillWatchingItems, setStillWatchingItems] = useState<any[]>([]);

  // Charger les éléments "still watching"
  const loadStillWatchingItems = async () => {
    try {
      const { getCachedData } = await import('../utils/tizenStorage');
      let stillWatching = await getCachedData('still_watching');
      console.log("stillwatching in mediaPage", stillWatching);
      
      if (!Array.isArray(stillWatching)) {
        stillWatching = [];
      }

      // Assert type to any[]
      const stillWatchingArray = stillWatching as any[];
      console.log("stillWatchingArray", stillWatchingArray);
      
      // Filtrer par type de média
      const filtered = stillWatchingArray.filter((item: any) => {
        if (mediaType === 'movie') {
          return item.mediaType === 'movie';
        } else if (mediaType === 'series') {
          return item.mediaType === 'series';
        }
        // Pour radio, quran et tv, on peut aussi filtrer si nécessaire
        return item.mediaType === mediaType;
      });

      setStillWatchingItems(filtered);
      console.log("StillWatchingItems", filtered);
    } catch (error) {
      console.error('Error loading still watching items:', error);
      setStillWatchingItems([]);
    }
  };

  // Charger les éléments au démarrage
  useEffect(() => {
    loadStillWatchingItems();
  }, [mediaType]);

  return {
    stillWatchingItems,
    loadStillWatchingItems,
    setStillWatchingItems
  };
};
