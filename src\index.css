@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #333232;
  --sidebar: #bb2c2c;
  --rate: #ffd700;
  --primary: #6563ff   ;
}

.scroll-container {
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

/* Pour empêcher le débordement pendant le scroll */
.overflow-y-auto {
  scroll-padding: 20px 0;
}

/* Cache la scrollbar tout en permettant le scroll */
.overflow-y-auto::-webkit-scrollbar {
  display: none;
}

.left-30 {
  left: 53%;
}

/* Styles pour la barre de recherche */
.wrap {
  width: 53%;
  white-space: nowrap;
  position: absolute;
  top: 50px;
  left: 28%;
  z-index: 999;
}

.globalInputSearch {
  color: white;
  display: flex;
  align-items: center;
}

.searchTerm {
  width: 0px;
  border: none;
  padding: 0px;
  background-color: transparent;
  border : #6563ff 3px solid;
  height: 0px;
  border-radius: 5px;
  outline: none;
  color: #9DBFAF;
  margin-right: 10px;
}

.searchTerm:focus {
  width: 300px;
  height: 50px;
  padding: 5px;
  color: #fcfcfc;
}

.searchbtn {
  width: auto;
  height: 50px;
  padding :10px;
  text-align: center;
  color: white;
  border-radius: 5px 5px 5px 5px;
  font-size: 20px;
  background-color: transparent;
  border: #6563ff 3px solid;
  cursor: pointer;
  margin-right: 10px;
}

.searchbtn:focus {
  background-color: #6563ff;
}


/* Styles pour les catégories */
.categories-container {
  overflow: hidden;
}

.categories-scroll {
  display: inline-flex;
  gap: 10px;

}

.category-btn {
  padding: 8px 15px;
  background-color: transparent;
  color: white;
  border: 3px solid #6563ff;
  border-radius: 5px;
  cursor: pointer;
  font-size: 20px;
  white-space: nowrap;
  transform: scale(1);
  transition: all 0.3s ease;
}

/* Style quand le bouton est focus (navigation clavier) */
.category-btn.focused {
  background-color: #6563ff;
  color: #fff;
}

/* Style quand la catégorie est active (sélectionnée) */
.category-btn.active-category {
  background-color: #a1a0ff;
  color: #fff;
}

/* Style quand les deux états sont actifs */
.category-btn.focused.active-category {
  transform: scale(1.05);
  box-shadow: 0 0 10px rgba(101, 99, 255, 0.7);
}


/* Style pour le texte */
.globalInputSearch span {
  padding-right: 10px;
  font-size: 20px;
}

/* Styles pour la liste VodContainerBloc */

.VodContainerBloc {
  position: relative;
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}


.VodContainerBlocScale {
  border : #6563ff 4px solid;
}

.VodContainerBlocli {
  position: relative;
  margin-bottom: 15px;
}

.VodContainerBlocImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 5%;
  transition: all 0.3s;
}


.texteListePlay {
  width: 100%;
  text-align: center;
  font-size: 18px;
  color: white;
  margin-top: 8px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.texteListePlayScale {
  color: #6563ff;
  font-weight: bold;
}


.texteRate {
  position: absolute;
  top: 10px;
  right: 0px;
  background: rgba(0, 0, 0, 0.8);
  color: #ffd700;
  padding: 2px 6px;
  font-size: 20px;
  z-index: 2;
}

.texteFlag {
  position: absolute;
  top: 10px;
  left: -20px;
  color: red;
  background-color: white;
  width: 80px;
  transform: rotate(-45deg);
  font-size: 14px;
  text-align: center;
  z-index: 2;
  clip-path: polygon(26% 0, 75% 0, 100% 100%, 0% 100%);
}


/*Movie Detail*/

.icon-channel {
  position: absolute;
  left: 58%;
  right: 0px;
  z-index: 5;
  -webkit-mask-image: 
      linear-gradient(to top, 
          rgba(0, 0, 0, 0) 0%, 
          rgba(0, 0, 0, 0.5) 20%, 
          rgba(0, 0, 0, 1) 30%, 
          rgba(0, 0, 0, 1) 100%),
      linear-gradient(to right, 
          rgba(0, 0, 0, 0) 0%, 
          rgba(0, 0, 0, 0.5) 20%, 
          rgba(0, 0, 0, 1) 30%, 
          rgba(0, 0, 0, 1) 100%);
  -webkit-mask-composite: destination-in;
}

.sep-title.titre {
  font-size: 100px;
  margin-bottom: 50px;
  width: 100%;
  color: #f44336;
  opacity: 1;
  z-index: 5;
}

.sep-title.actor {
  margin-bottom: 30px;
  color: #FFFFFF;
  opacity: 1;
}

.sep-title .vodTitre {
  font-size: 35px;
  margin-bottom: -30px;
}

.sep-title.back img {
  position: absolute;
  z-index: 1;
  top: -100px;
  left: -50px;
  width: 2000px;
  height: 1200px;
  opacity: 0.35;
}

.notFoundEpg {
  color: #fff;
  font-size: 30px;
  text-align: center;
  margin: 28% auto;
}

#listEpg {
  opacity: 1;
}

.play-icon-epg {
  position: relative;
  z-index: 10;
  outline-offset: 4px;
  border-radius: 12px; 
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.play-icon-epg.active-btn {
  outline: 5px solid #6563ff;
  box-shadow: 0 0 12px #6563ff;
  transform: scale(1.05);
}

.trailer-text {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  white-space: nowrap;
}


#av-player-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}





.player-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 20%;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px;
  box-sizing: border-box;
  transform: translateY(0);
  transition: transform 0.3s ease;
  z-index: 1000;
}

.player-panel.hidden {
  transform: translateY(100%);
}

.block1 {
  margin-bottom: 20px;
}

.name-channel {
  font-size: 24px;
  margin-bottom: 10px;
}

.progress-bar {
  width: 100%;
  height: 5px;
  background-color: #333;
  border-radius: 2px;
  position: relative;
}

.progress-bar.seek-mode {
  height: 8px;
  background-color: #444;
}

.progress-bar .playing {
  height: 100%;
  background-color: #e50914;
  border-radius: 2px;
}

.seek-indicator {
  position: absolute;
  top: -2px;
  width: 12px;
  height: 12px;
  background-color: #ffd700;
  border-radius: 50%;
  border: 2px solid #fff;
  transform: translateX(-50%);
  z-index: 10;
}

.blockMenu {
  display: flex;
  margin-bottom: 20px;
}

.menu {
  padding: 10px 20px;
  margin-right: 15px;
  background-color: #333;
  border-radius: 4px;
  cursor: pointer;
}

.menu.active-btn {
  background-color: #e50914;
}

.btn-block {
  display: flex;
}

.item {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  background-color: #333;
  border-radius: 4px;
  cursor: pointer;
}

.item.active-btn {
  background-color: #e50914;
}

.item img {
  width: 80%;
  height: 80%;
  object-fit: contain;
}

.control-btn {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #333;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
}

.play-btn {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
}

.play-btn.active-btn {
  background-color: #333;
  transform: scale(1.1);
}

.control-btn.active-btn {
  background-color: #e50914;
  transform: scale(1.1);
}

.audio-options, .subtitle-options {
  display: flex;
  gap: 10px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  margin-top: 10px;
}

.audio-option, .subtitle-option {
  padding: 8px 12px;
  background-color: #333;
  border-radius: 4px;
  cursor: pointer;
}

.audio-option.active-btn, .subtitle-option.active-btn {
  background-color: #e50914;
}

/* Animation de défilement pour les noms longs */
@keyframes scroll-text {
  0% {
    transform: translateX(0%);
  }
  25% {
    transform: translateX(0%);
  }
  75% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-scroll {
  animation: scroll-text 8s ease-in-out infinite;
  white-space: nowrap;
  display: inline-block;
}

/* Styles pour le spectre audio numérique */
.audio-spectrum-container {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(20, 20, 40, 0.9) 100%);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 0 50px rgba(100, 200, 255, 0.3);
  border: 2px solid rgba(100, 200, 255, 0.2);
  backdrop-filter: blur(10px);
  min-width: 800px;
}

.spectrum-title h2 {
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
}

.spectrum-title p {
  text-shadow: 0 0 15px rgba(100, 200, 255, 0.8);
}

.audio-spectrum {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 220px;
  gap: 6px;
  padding: 0 30px;
  border-radius: 10px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
}

.spectrum-bar {
  width: 12px;
  min-height: 4px;
  border-radius: 6px 6px 0 0;
  transition: height 0.1s ease-out, background-color 0.1s ease-out;
  position: relative;
  background: linear-gradient(to top, currentColor, rgba(255, 255, 255, 0.8));
}

.spectrum-bar::after {
  content: '';
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 16px;
  height: 3px;
  background: inherit;
  border-radius: 50%;
  filter: blur(1px);
  opacity: 0.8;
}

.spectrum-info p {
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Contrôles radio */
.radio-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.radio-control-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(100, 200, 255, 0.3);
  border-radius: 15px;
  padding: 15px 20px;
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(5px);
}

.radio-control-btn:hover,
.radio-control-btn.active-btn {
  background: rgba(100, 200, 255, 0.2);
  border-color: rgba(100, 200, 255, 0.8);
  box-shadow: 0 0 20px rgba(100, 200, 255, 0.4);
  transform: scale(1.05);
}

/* Indicateur LIVE pour les radios */
.radio-status-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 20px;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
}

.live-dot {
  width: 12px;
  height: 12px;
  background: #ff4444;
  border-radius: 50%;
  animation: pulse-live 2s infinite;
  box-shadow: 0 0 10px #ff4444;
}

@keyframes pulse-live {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Styles pour l'indicateur de chargement radio */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.loading-dots {
  display: flex;
  gap: 5px;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #4AADF7;
  animation: loading-bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-spectrum {
  min-height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #666;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  padding: 0 30px;
}


/* Structure générale */
.tv-player-ui {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Blockbouq - Affichage des bouquets */
.blockbouq {
  position: absolute;
  width: 30%;
  bottom: 60%;
  right: 10px;
  color: white;
  font-family: 'RobotoRegular';
}

.blockbouq .name-bq {
  position: absolute;
  top: 30px;
  right: 0;
  color: #d30b39;
  font-size: 50px;
  font-weight: bold;
  margin-right: 50px;
}

.blockbouq .arrow {
  padding-left: 10px;
}

.blockbouq .name-bqNext {
  position: absolute;
  top: 100px;
  right: 100px;
  color: #ffffff;
  font-size: 30px;
  font-weight: bold;
}

.blockbouq .name-bqLast {
  position: absolute;
  top: 0px;
  right: 100px;
  color: #ffffff;
  font-size: 30px;
  font-weight: bold;
}

/* Block1 - Infos de la chaîne */
.block1 {
  width: 100%;
  margin: 0 0 100px 50px;
  height: auto;
  color: white;
}

.block1 .name-channel {
  color: #fff;
  font-size: 30px;
  margin-bottom: 20px;
}

.block1 .now-play-epg {
  color: #fff;
  font-size: 39px;
  width: 95%;
}

.block1 .now-play-epg span {
  margin: 0 0 0 10px;
}

.block1 .next-play-epg {
  color: #fff;
  font-size: 39px;
  width: 95%;
}

.block1 .next-play-epg span {
  margin: 0 0 0 10px;
}

.block1 .progress-bar {
  width: 92%;
  margin: 0;
  height: 8px;
  background-color: #888;
  border-radius: 10px;
  margin: 20px 0 -60px 0;
}

.block1 .progress-bar .playing {
  width: auto;
  height: 100%;
  border-radius: 10px;
  background-color: #fff;
}

/* Block2 - Liste des chaînes */
.block2 {
  width: 100%;
  height: 210px;
  position: relative;
  overflow: hidden;
}

/* Liste des chaînes */
.channel-list {
  display: flex;
  overflow-x: auto;
  padding: 20px 0;
  scroll-behavior: smooth;
}

.channel-item {
  flex: 0 0 auto;
  width: 130px;
  margin: 0 15px;
  text-align: center;
}

.channel-item.active {
  transform: scale(1.1);
}

.channel-item img {
  border-radius: 50%;
  width: 100px;
  height: 100px;
  object-fit: cover;
  margin-bottom: 10px;
}

.channel-item .channel-name {
  font-size: 16px;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


/* StartScreen Styles */
.start-screen {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 991;
  top: 0;
  left: 0;
  display: flex;
}

.start-screen .left-block {
  width: 65%;
  height: 100%;
  background-color: #333232;
  display: flex;
  align-items: center;
}

.start-screen .left-block .block-info {
  margin-left: 100px;
  color: white;
}

.start-screen .left-block .block-info .title {
  color: #fff;
  font-size: 50px;
  margin: 0 0 15px 0;
  font-weight: bold;
}

.start-screen .left-block .block-info .description {
  color: #c7c5c5;
  font-size: 48px;
  line-height: 1.2;
}

.start-screen .right-block {
  width: 35%;
  height: 100%;
  background-color: #bb2c2c;
  display: flex;
  align-items: center;
}

.start-screen .right-block .block-list {
  margin-left: 60px;
  width: calc(100% - 60px);
}

.start-screen .right-block .block-list .item {
  color: #fff;
  font-size: 37px;
  width: 96%;
  padding: 17px 5px 17px 10px;
  margin-bottom: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease;
}

.start-screen .right-block .block-list .item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.start-screen .right-block .block-list .item.selected {
  background: rgba(255, 255, 255, 0.2);
  border-left: 4px solid #fff;
}

.start-screen .right-block .block-list .form-input {
  margin-right: 15px;
  height: 25px;
  width: 25px;
  flex-shrink: 0;
}

.start-screen .right-block .block-list .form-input:focus {
  outline: 2px solid #fff;
}

/* Focus styles for TV remote navigation */
.start-screen .right-block .block-list .item:focus {
  outline: 2px solid #fff;
  outline-offset: 2px;
}

/* Media queries for different screen sizes */
@media (max-width: 1920px) {
  .start-screen .left-block .block-info .title {
    font-size: 45px;
  }
  
  .start-screen .left-block .block-info .description {
    font-size: 42px;
  }
  
  .start-screen .right-block .block-list .item {
    font-size: 34px;
  }
}

@media (max-width: 1366px) {
  .start-screen .left-block .block-info {
    margin-left: 60px;
  }
  
  .start-screen .left-block .block-info .title {
    font-size: 40px;
  }
  
  .start-screen .left-block .block-info .description {
    font-size: 36px;
  }
  
  .start-screen .right-block .block-list {
    margin-left: 40px;
  }
  
  .start-screen .right-block .block-list .item {
    font-size: 30px;
  }
}


#welcome,
.welcome-container {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  z-index: 9998;
  background-color: #bb2c2c;
}

#welcome .title-info,
.welcome-container .title-info {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  color: #fff;
  width: 413px;
  margin: auto;
  height: 241px;
}

#welcome .title-info .logo,
.welcome-container .title-info .logo {
  width: 400px;
  height: auto;
  display: block;
  margin: 0 auto;
}

#welcome .title-info span,
.welcome-container .title-info span,
#welcome .title-info .title,
.welcome-container .title-info .title {
  display: block;
  color: #ffffff;
  font-weight: 400;
  line-height: 80px;
  font-size: 55px;
  text-align: center;
  margin-top: 20px;
}

#welcome #splash_pattern,
.welcome-container .splash-pattern {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

/* Utility class for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Pour forcer le YouTube à remplir le conteneur parent */
.youtube-full-bg iframe {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  position: absolute;
  pointer-events: none;
  z-index: 1;
}

.loader {
  width: 5%;
  padding: 8px;
  aspect-ratio: 1;
  border-radius: 50%;
  background: #bb2c2c;
  z-index: 10;
  --_m: 
    conic-gradient(#0000 10%,#000),
    linear-gradient(#000 0 0) content-box;
  -webkit-mask: var(--_m);
          mask: var(--_m);
  -webkit-mask-composite: source-out;
          mask-composite: subtract;
  animation: l3 1s infinite linear;
}
@keyframes l3 {to{transform: rotate(1turn)}}


/* Amélioration du contraste pour la lisibilité TV */
.tv-text {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.tv-text-bright {
  text-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.8),
    0 0 8px rgba(255, 255, 255, 0.1);
}
