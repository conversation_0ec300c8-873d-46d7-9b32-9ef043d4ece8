import React, { useEffect, useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { useWelcome } from '../hooks/useWelcome';

interface WelcomePageProps {
  onComplete?: () => void;
  showSplash?: boolean;
  autoHide?: boolean;
  duration?: number;
}

const WelcomePage: React.FC<WelcomePageProps> = ({ 
  onComplete, 
  showSplash = false, 
  autoHide = true,
  duration = 4000 
}) => {
  const { t } = useTranslation();
  const { currentTitle, setPromoScreen, clear } = useWelcome();
  const [mounted, setMounted] = useState(true);

  useEffect(() => {
    // Initialize the welcome screen
    setMounted(true);
    
    if (showSplash) {
      setPromoScreen();
    }

    // Auto-hide after specified duration if autoHide is enabled
    if (autoHide) {
      const timer = setTimeout(() => {
        handleComplete();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [showSplash, autoHide, duration, setPromoScreen]);

  const handleComplete = () => {
    clear();
    setMounted(false);
    if (onComplete) {
      onComplete();
    }
  };

  if (!mounted) {
    return null;
  }

  return (
    <div id="welcome" className="welcome-container">
      <div className="title-info">
        <img 
          src="/smart_tv.svg" 
          alt="Logo" 
          className="logo"
          onError={(e) => {
            (e.target as HTMLImageElement).src = '/NoLogo.png';
          }}
        />
        <span className="title">
          {currentTitle || t('welcome') || 'Welcome'}
        </span>
      </div>
      
      {showSplash && (
        <div id="splash_pattern" className="splash-pattern">
          {/* Splash pattern content */}
        </div>
      )}
    </div>
  );
};

export default WelcomePage;
