{"name": "package.json", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "clean": "node clean.cjs", "build": "tsc -b && npm run clean && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@noriginmedia/react-spatial-navigation": "^2.12.9", "@types/tizen-tv-webapis": "^2.0.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-js-spatial-navigation": "^0.0.5", "react-router-dom": "^7.5.3", "react-youtube": "^10.1.0", "tizen-tv-webapis": "^2.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^22.15.15", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}