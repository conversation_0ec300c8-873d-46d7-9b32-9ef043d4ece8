import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import Logo from "../components/Logo";
import { api } from "../utils/api";
import { AUTH_STATUS } from "../utils/core";
import { useTranslation } from "../hooks/useTranslation";
import { RETURN_KEY, RETURN_KEY_CODE } from "../utils/keysCode";

const LoginPage = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [code, setCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null); 
  const buttonRef = useRef<HTMLButtonElement>(null);

  const loginOptions = [
    t("login"),
    t("loginWithCode"),
    t("loginWithDefaultCode"),
  ];

  const handleLogin = async () => {
    if (!code.trim()) {
      alert(t("pleaseInputCode"));
      return;
    }

    setIsLoading(true);

    try {
      const result = await api.login(code);
      
      if (typeof result === 'string') {
        alert(result);
        return;
      }

      if (result.authentification[0].status === AUTH_STATUS.SUCCESS) {
        alert(t("loginSuccessful") + result.authentification[0].msg);
        navigate("/");
      } else {
        alert(result.authentification[0].msg || t("invalidCode"));
      }
    } catch (error) {
      console.error("Login error:", error);
      alert(t("loginFailed"));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.keyCode === 10009) {
        e.preventDefault();
        navigate(-1);
        return;
      }

      if (e.key === "ArrowDown" || e.key === "ArrowUp") {
        setSelectedIndex((prev) => (prev === 0 ? 1 : 0));
      } if (e.key === "Enter" && selectedIndex === 1) {
        handleLogin();
      } else if (e.key === RETURN_KEY || e.keyCode === RETURN_KEY_CODE) {
        navigate(-1);
      }

    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [navigate, selectedIndex, code]);

  useEffect(() => {
    if (selectedIndex === 0 && inputRef.current) {
      inputRef.current.focus();
    }else if (selectedIndex === 1 && buttonRef.current) {
      buttonRef.current.focus();
    }
  }, [selectedIndex, inputRef, buttonRef]);

  return (
    <div className="flex h-screen text-white font-sans">
      <Logo />
      <div className="w-2/3 flex bg-black flex-col justify-center gap-6 pl-24">
        {loginOptions.map((label, index) => (
          <div key={index} className="text-6xl text-gray-400">
            {label}
          </div>
        ))}
      </div>

      <div className="w-1/3 bg-sidebar flex flex-col justify-center items-start gap-6 pl-24">
        <input
          ref={inputRef}
          type="text"
          placeholder={t("inputYourCode")}
          value={code}
          onChange={(e) => setCode(e.target.value)}
          disabled={isLoading}
          className={`px-4 py-2 text-5xl w-full outline-none placeholder-current ${
            selectedIndex === 0 ? "bg-white/10" : "bg-transparent"
          } ${isLoading ? "opacity-50" : ""}`}
        />
        <button
          className={`px-4 py-2 text-5xl ${
            selectedIndex === 1 ? "bg-white/10 text-white" : ""
          }`}
          ref={buttonRef}
          onClick={handleLogin}
          disabled={isLoading}
        >
          {isLoading ? t("loggingIn") : t("ok")}
        </button>
      </div>
    </div>
  );
};

export default LoginPage;
