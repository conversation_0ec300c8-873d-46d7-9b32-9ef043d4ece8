import { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { SubCategory } from '../types/movies';
import { TVCategory, TVChannel, EPGProgram } from '../types/tv';
import { getCachedData, setCachedData } from '../utils/tizenStorage';
import { formatPlayerTime } from '../utils/formatTime';
import { api } from '../utils/api';
import Logo from '../components/Logo';

const Player = () => {
  const { state } = useLocation();
  console.log("state", state)

  console.log("state?.channels && state.channels.length > 0", state?.channels && state.channels.length > 0)
  const navigate = useNavigate();
  const media = state?.media as SubCategory;
  const backlink = state?.backlink as string;
  console.log("backlink", backlink)
  const mediaType = state?.type as string;
  console.log("mediaType", mediaType)
  const startPosition = state?.startPosition as number | undefined;
  const autoPlay = state?.autoPlay as boolean | undefined;

  const playerPanelRef = useRef<HTMLDivElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playerReady, setPlayerReady] = useState(false);
  const [showControls, setShowControls] = useState(false);
  console.log("showControls", showControls)
  const [activeControl, setActiveControl] = useState('1_playpause');
  //const [activeMenu, setActiveMenu] = useState('0_player');
  const [navigation, setNavigation] = useState<'menu' | 'controls' | 'audio' | 'subtitle' | 'seek'>('menu');
  // États pour le mode seek
  const [seekMode, setSeekMode] = useState(false);
  const [seekPosition, setSeekPosition] = useState(0);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  //  pour suivre si la vidéo est en pause
  const [isPaused, setIsPaused] = useState(false);
  // pour éviter les clics trop rapides
  const lastActionTimeRef = useRef<number>(0);
  // pour suivre le temps de lecture
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const timeUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  // Ajoutez cet état pour suivre le chargement
  const [isLoading, setIsLoading] = useState(false);
  // État pour les messages d'erreur
  const [errorMessage, setErrorMessage] = useState<string>('');
  // Refs pour la gestion des touches maintenues
  const seekIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const keyPressedRef = useRef<string | null>(null);

  // États pour l'interface TV
  const [tvCategories, setTvCategories] = useState<TVCategory[]>([]);
  const [tvChannels, setTvChannels] = useState<TVChannel[]>([]);
  // Loading state for TV interface
  const [tvInterfaceLoading, setTvInterfaceLoading] = useState(false);
  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState(0);
  const [selectedChannelIndex, setSelectedChannelIndex] = useState(0);
  const [tvInterfaceActive, setTvInterfaceActive] = useState(false);
  const [currentChannelLink, setCurrentChannelLink] = useState<string>('');

  // États pour l'interface Radio
  const [radioCategories, setRadioCategories] = useState<any[]>([]);
  const [radioStations, setRadioStations] = useState<any[]>([]);
  const [selectedRadioCategoryIndex, setSelectedRadioCategoryIndex] = useState(0);
  const [selectedRadioStationIndex, setSelectedRadioStationIndex] = useState(0);
  const [radioInterfaceActive, setRadioInterfaceActive] = useState(false);
  const [currentRadioLink, setCurrentRadioLink] = useState<string>('');

  // États pour l'EPG
  const [currentEPG, setCurrentEPG] = useState<{
    current: EPGProgram | null;
    next: EPGProgram | null;
  }>({ current: null, next: null });
  const [showEPG, setShowEPG] = useState(false);

  // États pour les sous-titres
  const [subtitles, setSubtitles] = useState("");
  const [subtitleTracks, setSubtitleTracks] = useState<any[]>([]);
  const [currentSubtitleTrack, setCurrentSubtitleTrack] = useState(-1);
  const subtitleTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // États pour mémoriser les sélections actives
  const [selectedAudioTrack, setSelectedAudioTrack] = useState<string>('');
  const [selectedSubtitleOption, setSelectedSubtitleOption] = useState<string>('');

  // États pour le message "Next Episode/Surah"
  const [showNextMessage, setShowNextMessage] = useState(false);
  const [nextMessageProgress, setNextMessageProgress] = useState(0);
  const nextMessageTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const nextMessageIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Référence pour le conteneur des chaînes TV
  const tvChannelsContainerRef = useRef<HTMLDivElement>(null);

  // Référence pour le conteneur des stations radio
  const radioStationsContainerRef = useRef<HTMLDivElement>(null);

  // Configuration des contrôles
  const controlsConfig = {
    menu: {
      '0_player': { id: 'player', title: 'Player Controls' },
      '1_audio': { id: 'audio', title: 'Audio Tracks' },
      '2_subtitle': { id: 'subtitle', title: 'Subtitles' }
    },
    // Nouvelle configuration pour la nouvelle disposition
    playerControls: {
      '0_previous': { id: 'previous', icon: '/previous.svg' },
      '1_playpause': { id: 'playpause', icon: isPlaying ? '/pause.svg' : '/play.svg' },
      '2_next': { id: 'next', icon: '/next.svg' },
      '3_audio': { id: 'audio', icon: '/audio.svg' },
      '4_subtitle': { id: 'subtitle', icon: '/subtitle.svg' }
    },
    audio: media?.language?.split(',').reduce((acc, lang, index) => {
      acc[`${index}_${lang}`] = lang;
      return acc;
    }, {} as Record<string, string>),
    subtitle: media?.sub?.split(',').reduce((acc, sub, index) => {
      acc[`${index}_${sub}`] = sub;
      return acc;
    }, {} as Record<string, string>)
  };

  // Fonctions utilitaires pour l'EPG
  const decodeBase64 = (encodedText: string): string => {
    try {
      // Décoder le Base64 puis décoder l'UTF-8
      const binaryString = atob(encodedText);
      const bytes = new Uint8Array(binaryString.length);

      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Utiliser TextDecoder pour gérer l'UTF-8
      const decoder = new TextDecoder('utf-8');
      return decoder.decode(bytes);
    } catch (error) {
      console.error('Error decoding base64:', error);
      // Fallback vers la méthode simple si ça échoue
      try {
        return atob(encodedText);
      } catch (fallbackError) {
        console.error('Fallback decode also failed:', fallbackError);
        return '';
      }
    }
  };

  const parseEPGData = (channel: TVChannel): { current: EPGProgram | null; next: EPGProgram | null } => {
    let current: EPGProgram | null = null;
    let next: EPGProgram | null = null;

    if (channel.current && channel.desc_current && channel.start_current && channel.end_current) {
      current = {
        title: decodeBase64(channel.current),
        description: decodeBase64(channel.desc_current),
        startTime: parseInt(channel.start_current) * 1000, // Convert to milliseconds
        endTime: parseInt(channel.end_current) * 1000
      };
    }

    if (channel.next && channel.desc_next && channel.start_next && channel.end_next) {
      next = {
        title: decodeBase64(channel.next),
        description: decodeBase64(channel.desc_next),
        startTime: parseInt(channel.start_next) * 1000,
        endTime: parseInt(channel.end_next) * 1000
      };
    }

    return { current, next };
  };

  const formatEPGTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getProgressPercentage = (current: EPGProgram): number => {
    const now = Date.now();
    const totalDuration = current.endTime - current.startTime;
    const elapsed = now - current.startTime;
    return Math.max(0, Math.min(100, (elapsed / totalDuration) * 100));
  };

  const handleNextEpisode = () => {
    console.log("state?.episodes", state?.episodes)
    console.log("state?.surahs", state?.surahs)

    // Pour le Coran, utiliser la liste des sourates
    if (mediaType === 'quran' && state?.surahs) {
      savePlaybackPosition();

      const currentSurahIndex = state.surahs.findIndex(
        (surah: any) => surah.link === media.link
      );

      if (currentSurahIndex < state.surahs.length - 1) {
        const nextSurah = state.surahs[currentSurahIndex + 1];
        // Arrêter la lecture actuelle avant de naviguer
        handleStop();
        navigate("/player", {
          state: {
            media: nextSurah,
            surahs: state.surahs,
            type: 'quran',
            startPosition: 0,
            autoPlay: true, // Ajouter un flag pour le démarrage automatique
            navigationState: state.navigationState
          }
        });
      }
      return;
    }

    // Pour les séries/épisodes, utiliser la logique existante
    if (!state?.episodes) return;

    savePlaybackPosition();

    const currentEpisodeIndex = state.episodes.findIndex(
      (ep: SubCategory) => ep.link === media.link
    );

    if (currentEpisodeIndex < state.episodes.length - 1) {
      const nextEpisode = state.episodes[currentEpisodeIndex + 1];
      // Arrêter la lecture actuelle avant de naviguer
      handleStop();
      navigate("/player", {
        state: {
          media: nextEpisode,
          episodes: state.episodes,
          startPosition: 0,
          autoPlay: true, // Ajouter un flag pour le démarrage automatique
          seriesNavigationState: state.seriesNavigationState
        }
      });
    }
  };

  const handlePreviousEpisode = () => {
    console.log("state?.episodes", state?.episodes)
    console.log("state?.surahs", state?.surahs)

    // Pour le Coran, utiliser la liste des sourates
    if (mediaType === 'quran' && state?.surahs) {
      savePlaybackPosition();

      const currentSurahIndex = state.surahs.findIndex(
        (surah: any) => surah.link === media.link
      );

      if (currentSurahIndex > 0) {
        const prevSurah = state.surahs[currentSurahIndex - 1];
        // Arrêter la lecture actuelle avant de naviguer
        handleStop();
        navigate("/player", {
          state: {
            media: prevSurah,
            surahs: state.surahs,
            type: 'quran',
            startPosition: 0,
            autoPlay: true, // Ajouter un flag pour le démarrage automatique
            navigationState: state.navigationState
          }
        });
      }
      return;
    }

    // Pour les séries/épisodes, utiliser la logique existante
    if (!state?.episodes) return;

    savePlaybackPosition();
    const currentEpisodeIndex = state.episodes.findIndex(
      (ep: SubCategory) => ep.link === media.link
    );

    if (currentEpisodeIndex > 0) {
      const prevEpisode = state.episodes[currentEpisodeIndex - 1];
      // Arrêter la lecture actuelle avant de naviguer
      handleStop();
      navigate("/player", {
        state: {
          media: prevEpisode,
          episodes: state.episodes,
          startPosition: 0,
          autoPlay: true, // Ajouter un flag pour le démarrage automatique
          seriesNavigationState: state.seriesNavigationState
        }
      });
    }
  };

  // Initialisation du player
  useEffect(() => {
    if (typeof webapis === 'undefined' || typeof webapis.avplay === 'undefined') {
      console.error('Tizen AVPlay API not available');
      return;
    }

    const listener = {
      onbufferingstart: () => {
        console.log('Buffering started');
        setIsLoading(true);
      },
      onbufferingcomplete: () => {
        console.log('Buffering complete');
        setIsLoading(false);
      },
      oncurrentplaytime: (currentTime: number) => {
        // Mettre à jour le temps de lecture lorsque l'événement est déclenché
        if (currentTime >= 0 && !isNaN(currentTime)) {
          setCurrentTime(currentTime);
        }
        if (isPlaying) {
          resetControlsTimeout();
        }
      },
      onplaybackcompleted: () => {
        console.log('Playback completed');
        handleStop();
      },
      onplaybackstarted: () => {
        console.log('Playback started');
        setIsPaused(false);
      },
      onplaystatechange: (state: string) => {
        console.log('Play state changed:', state);
        // Mettre à jour l'état de pause en fonction de l'état réel du lecteur
        if (state === 'PAUSED') {
          setIsPaused(true);
        } else if (state === 'PLAYING') {
          setIsPaused(false);
        }
      },
      onsubtitlechange: (_duration: string, text: string, _type: string, _attributes: any) => {
        // Nettoyer le timeout précédent
        if (subtitleTimeoutRef.current) {
          clearTimeout(subtitleTimeoutRef.current);
        }

        // Nettoyer et filtrer le texte des sous-titres
        if (text && typeof text === 'string') {
          // Supprimer les balises HTML/XML qui pourraient être présentes
          const cleanText = text
            .replace(/<[^>]*>/g, '') // Supprimer les balises HTML/XML
            .replace(/&lt;/g, '<')   // Décoder les entités HTML
            .replace(/&gt;/g, '>')
            .replace(/&amp;/g, '&')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .trim(); // Supprimer les espaces en début/fin

          // Afficher seulement si le texte nettoyé n'est pas vide et ne contient pas que des chiffres
          if (cleanText && !/^\d+$/.test(cleanText)) {
            setSubtitles(cleanText);

            // Programmer l'effacement des sous-titres après 5 secondes
            subtitleTimeoutRef.current = setTimeout(() => {
              setSubtitles('');
            }, 5000);
          } else {
            setSubtitles(''); // Effacer si c'est juste des nombres ou vide
          }
        } else {
          setSubtitles('');
        }
      },
      onerror: (eventType: string) => {
        console.error('Player error:', eventType);
        handleStop();
      },
      onstreamcompleted: () => {
        console.log('Stream completed');
        handleNextEpisode();
      }
    };

    webapis.avplay.setListener(listener);
    setPlayerReady(true);

    return () => {
      if (webapis.avplay.getState() !== "IDLE") {
        webapis.avplay.stop();
        webapis.avplay.close();
      }
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      if (timeUpdateIntervalRef.current) {
        clearInterval(timeUpdateIntervalRef.current);
      }
      if (subtitleTimeoutRef.current) {
        clearTimeout(subtitleTimeoutRef.current);
      }
      if (nextMessageTimeoutRef.current) {
        clearTimeout(nextMessageTimeoutRef.current);
      }
      if (nextMessageIntervalRef.current) {
        clearInterval(nextMessageIntervalRef.current);
      }
      stopContinuousSeek(); // Nettoyer les intervalles de seek
    };
  }, [media, state?.episodes, state?.surahs, mediaType]); // Ajouter les dépendances pour que les listeners se mettent à jour

  // Ajoutez un nouvel effet qui s'exécute quand playerReady change
  useEffect(() => {
    if (playerReady && media?.link) {
      console.log('Player ready and media link available, starting playback');
      console.log('AutoPlay flag:', autoPlay);
      // Initialiser le lien de la chaîne courante pour la TV
      if (mediaType === 'tv') {
        setCurrentChannelLink(media.link);
      }
      // Initialiser le lien de la station courante pour la Radio
      if (mediaType === 'radio') {
        setCurrentRadioLink(media.link);
      }
      // Démarrer automatiquement si c'est spécifié ou si ce n'est pas une navigation depuis un autre épisode
      if (autoPlay !== false) {
        handlePlay();
      }
    }
  }, [playerReady, media, autoPlay]);

  const resetControlsTimeout = () => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    controlsTimeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 10000);
  };

  // Fonction pour afficher les messages d'erreur
  const showErrorMessage = (message: string) => {
    setErrorMessage(message);
  }

  const handlePlay = () => {
    if (!media?.link || !playerReady) return;

    try {
      // Si on est en train de jouer et que ce n'est pas un autoPlay, arrêter
      if (isPlaying && !autoPlay) {
        handleStop();
        return;
      }

      setIsLoading(true);
      const currentState = webapis.avplay.getState();
      if (currentState !== "IDLE") {
        webapis.avplay.stop();
      }

      webapis.avplay.open(media.link);
      webapis.avplay.setDisplayRect(0, 0, 1920, 1080);

      webapis.avplay.prepareAsync(
        () => {
          console.log('Playback prepared successfully');

          // Si une position de départ est spécifiée, y aller avant de commencer la lecture
          if (startPosition && startPosition > 0) {
            try {
              console.log(`Seeking to start position: ${startPosition}ms`);
              webapis.avplay.seekTo(startPosition);
              setCurrentTime(startPosition);
            } catch (seekError) {
              console.error('Error seeking to start position:', seekError);
            }
          }

          webapis.avplay.play();
          setIsPlaying(true);
          setIsPaused(false);
          setShowControls(true);
          setNavigation('controls');
          setActiveControl('1_playpause');
          resetControlsTimeout();

          // Initialiser les pistes de sous-titres
          try {
            const tracks = webapis.avplay.getTotalTrackInfo();
            console.log("All tracks", tracks)
            const textTracks = tracks.filter((t: any) => t.type === 'TEXT');
            setSubtitleTracks(textTracks);
            if (textTracks.length > 0) {
              webapis.avplay.setSelectTrack("TEXT", textTracks[0].index);
              setCurrentSubtitleTrack(0);
              // Initialiser la sélection par défaut pour les sous-titres
              const subtitleKeys = Object.keys(controlsConfig.subtitle || {});
              if (subtitleKeys.length > 0) {
                setSelectedSubtitleOption(subtitleKeys[0]);
              }
              // Afficher les informations détaillées du stream
              let currentStreamInfo = webapis.avplay.getCurrentStreamInfo();
              console.log("Current Stream Info:", currentStreamInfo);
            }

            // Initialiser la sélection par défaut pour l'audio
            const audioKeys = Object.keys(controlsConfig.audio || {});
            if (audioKeys.length > 0) {
              setSelectedAudioTrack(audioKeys[0]);
            }
          } catch (error) {
            console.error('Error initializing subtitle tracks:', error);
          }

          // Initialiser le temps de lecture et la durée
          try {
            const initialDuration = webapis.avplay.getDuration();
            if (initialDuration > 0 && !isNaN(initialDuration)) {
              setDuration(initialDuration);
            }
            if (!startPosition) {
              setCurrentTime(0);
            }
          } catch (error) {
            console.error('Error initializing time values:', error);
          }
        },
        (error) => {
          console.error('Prepare error:', error);
          setIsLoading(false);
          showErrorMessage('Erreur lors de la préparation de la lecture. Veuillez réessayer.');
        }
      );
    } catch (error) {
      console.error('Playback error:', error);
      setIsLoading(false);
      showErrorMessage('Erreur lors du démarrage de la lecture. Vérifiez votre connexion.');
    }
  };

  // Ajoutez cette fonction pour sauvegarder la position de lecture
  const savePlaybackPosition = async () => {
    if (!media || !media.link) return;

    try {
      const currentPos = webapis.avplay.getCurrentTime();
      if (currentPos > 0 && currentPos < duration * 0.95) { // Ne pas sauvegarder si on est à la fin (95%)
        console.log(`Saving playback position for media ${media.link}: ${currentPos}ms`);

        // Sauvegarder la position
        await setCachedData(`movie_position_${media.link}`, {
          position: currentPos,
          timestamp: Date.now(),
          duration: duration
        });

        // Ajouter à la liste "Still Watching"
        const stillWatching = Array.isArray(await getCachedData('still_watching'))
          ? await getCachedData('still_watching')
          : [];
        // Vérifier si le média existe déjà
        const stillWatchingArr = stillWatching as any[];
        const isSeriesType = state?.seriesNavigationState ? true : false;

        let existingIndex;
        if (isSeriesType && state?.seriesNavigationState?.series) {
          // Pour les séries, chercher par l'ID de la série
          existingIndex = stillWatchingArr.findIndex((item: any) =>
            item.mediaType === 'series' && item.id === state.seriesNavigationState.series.id
          );
        } else {
          // Pour les films, chercher par le lien du média
          existingIndex = stillWatchingArr.findIndex((item: any) => item.link === media.link);
        }
        const mediaData = {
          ...media,
          currentTime: currentPos,
          duration: duration,
          lastWatched: Date.now(),
          serie: state?.seriesNavigationState?.series,
          mediaType: mediaType || 'series'
        };

        console.log("mediadata", mediaData)

        // Déterminer quoi enregistrer selon le type de média
        let dataToSave;
        if (mediaData.mediaType === 'series' && mediaData.serie) {
          // Pour les séries, enregistrer les informations de la série avec les données de progression
          dataToSave = {
            ...mediaData.serie,
            currentTime: currentPos,
            duration: duration,
            lastWatched: Date.now(),
            mediaType: 'series',
            currentEpisode: media // Garder une référence à l'épisode actuel
          };
        } else {
          // Pour les films et autres médias, enregistrer mediaData complet
          dataToSave = mediaData;
        }

        if (existingIndex >= 0) {
          // Mettre à jour l'entrée existante
          stillWatchingArr[existingIndex] = dataToSave;
        } else {
          // Ajouter une nouvelle entrée
          stillWatchingArr.push(dataToSave);
        }

        // Trier par date de visionnage (les plus récents en premier)
        stillWatchingArr.sort((a: any, b: any) => b.lastWatched - a.lastWatched);

        // Garder seulement les 20 derniers
        await setCachedData('still_watching', stillWatchingArr.slice(0, 20));
      }
    } catch (error) {
      console.error('Error saving playback position:', error);
    }
  };

  const handleStop = () => {
    try {
      if (webapis.avplay.getState() !== "IDLE") {
        savePlaybackPosition();
        webapis.avplay.stop();
        webapis.avplay.close();
      }
      setIsPlaying(false);
      setIsPaused(false);
      setShowControls(false);
    } catch (error) {
      console.error('Stop error:', error);
    }
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (isPlaying) {
      setShowControls(true);
      resetControlsTimeout();
    }

    if (
      e.key === "Backspace" ||
      e.key === "Back" ||
      e.key === "Escape" ||
      (typeof e.keyCode !== "undefined" && e.keyCode === 10009)
    ) {
      // Si l'interface TV est active, la fermer
      if (mediaType === 'tv' && tvInterfaceActive) {
        setTvInterfaceActive(false);
        e.preventDefault();
        return;
      }

      // Si l'interface Radio est active, la fermer
      if (mediaType === 'radio' && radioInterfaceActive) {
        setRadioInterfaceActive(false);
        e.preventDefault();
        return;
      }

      // Pour la TV, si l'interface n'est pas active, naviguer directement en arrière
      if (mediaType === 'tv' && !tvInterfaceActive) {
        savePlaybackPosition();
        handleStop();
        if (state?.navigationState) {
          navigate("/live", {
            state: {
              ...state.navigationState,
              restoreNavigation: true
            }
          });
        } else {
          navigate(-1);
        }
        e.preventDefault();
        return;
      }

      // Pour la Radio, si l'interface n'est pas active, naviguer directement en arrière
      if (mediaType === 'radio' && !radioInterfaceActive) {
        handleStop();
        if (state?.navigationState) {
          navigate("/radio", {
            state: {
              ...state.navigationState,
              restoreNavigation: true
            }
          });
        } else {
          navigate(-1);
        }
        e.preventDefault();
        return;
      }

      // Pour les autres types de médias, garder la logique existante
      if (isPlaying) {
        if (navigation === 'seek') {
          // En mode seek, revenir aux contrôles
          setNavigation('controls');
          setSeekMode(false);
          setActiveControl('1_playpause');
          stopContinuousSeek();
        } else if (navigation !== 'controls') {
          // Si on est dans un sous-menu, revenir au menu principal
          setNavigation('controls');
          setActiveControl(navigation === 'audio' ? '3_audio' : '4_subtitle');
        } else if (showControls) {
          // Si les contrôles sont affichés, les cacher
          setShowControls(false);
          if (controlsTimeoutRef.current) {
            clearTimeout(controlsTimeoutRef.current);
            controlsTimeoutRef.current = null;
          }
        } else {
          savePlaybackPosition();
          handleStop();
          if (state?.seriesNavigationState) {
            navigate("/series-description", {
              state: {
                ...state.seriesNavigationState,
                episodes: state.episodes,
                restoreNavigation: true
              }
            });
          } else if (state?.navigationState) {
            // Déterminer la route correcte selon le type de média
            let targetRoute = "/media";
            if (mediaType === 'radio') {
              targetRoute = "/radio";
            } else if (mediaType === 'quran') {
              targetRoute = "/coran";
            }

            // Retourner à la page appropriée avec restauration de l'historique de navigation
            navigate(targetRoute, {
              state: {
                ...state.navigationState,
                restoreNavigation: true
              }
            });
          } else {
            navigate(-1);
          }
        }
      } else {
        if (state?.seriesNavigationState) {
          navigate("/series-description", {
            state: {
              ...state.seriesNavigationState,
              episodes: state.episodes,
              restoreNavigation: true
            }
          });
        } else if (state?.navigationState) {
          // Retourner à la bonne page selon le type de média
          let targetRoute = "/media";
          if (mediaType === 'radio') {
            targetRoute = "/radio";
          } else if (mediaType === 'quran') {
            targetRoute = "/coran";
          }

          navigate(targetRoute, {
            state: {
              ...state.navigationState,
              restoreNavigation: true
            }
          });
        } else {
          navigate(-1);
        }
      }
      e.preventDefault();
      return;
    }

    switch (e.key) {
      case "Enter":
        // Si le message "Next Episode/Surah" est affiché, passer directement au suivant
        if (showNextMessage) {
          // Nettoyer les timeouts et intervalles du message
          if (nextMessageTimeoutRef.current) {
            clearTimeout(nextMessageTimeoutRef.current);
            nextMessageTimeoutRef.current = null;
          }
          if (nextMessageIntervalRef.current) {
            clearInterval(nextMessageIntervalRef.current);
            nextMessageIntervalRef.current = null;
          }
          setShowNextMessage(false);

          // Passer à l'épisode/sourate suivant(e)
          handleNextEpisode();
          e.preventDefault();
          return;
        }

        if (!isPlaying) {
          handlePlay();
        } else if (mediaType === 'tv') {
          // Pour la TV, Enter active/désactive l'interface de navigation
          if (!tvInterfaceActive) {
            setTvInterfaceLoading(true);

            // Si state.channels est disponible, l'utiliser directement
            if (state?.channels && state.channels.length > 0) {
              // Trouver l'index de la chaîne actuelle dans state.channels
              const playingIndex = state.channels.findIndex((ch: any) =>
                (ch.ch && ch.ch === currentChannelLink) || (ch.link && ch.link === currentChannelLink)
              );
              if (playingIndex !== -1) {
                setSelectedChannelIndex(playingIndex);
              } else {
                setSelectedChannelIndex(0);
              }
              setTvInterfaceLoading(false);
              setTvInterfaceActive(true);
              setShowControls(false);
            }
            // Sinon, utiliser la logique existante avec les catégories
            else if (currentChannelLink && tvCategories.length > 0) {
              // On recherche la catégorie et la chaîne correspondantes
              (async () => {
                for (let catIdx = 0; catIdx < tvCategories.length; catIdx++) {
                  const res = await api.fetchLiveTVChannels(tvCategories[catIdx].id);
                  const channels = typeof res !== 'string' && res.data.channels ? res.data.channels : [];
                  const playingIndex = channels.findIndex(ch => ch.ch === currentChannelLink);
                  if (playingIndex !== -1) {
                    setSelectedCategoryIndex(catIdx);
                    setTvChannels(channels);
                    setSelectedChannelIndex(playingIndex);
                    break;
                  }
                }
                setTvInterfaceLoading(false);
                setTvInterfaceActive(true);
                setShowControls(false);
              })();
            } else {
              setTvInterfaceLoading(false);
              setTvInterfaceActive(true);
              setShowControls(false);
            }
          } else {
            // Si l'interface TV est active, sélectionner la chaîne
            const channelsToUse = (state?.channels && state.channels.length > 0 ? state.channels : tvChannels);
            console.log("channelsToUse", channelsToUse);
            if (channelsToUse[selectedChannelIndex]) {
              const selectedChannel = channelsToUse[selectedChannelIndex];
              // Ne pas recharger si c'est la même chaîne que celle en cours
              const channelLink = selectedChannel.ch || selectedChannel.link;
              if (channelLink !== currentChannelLink) {
                switchToChannel(selectedChannel);
              }
            }
          }
        } else if (mediaType === 'radio') {
          // Pour les radios, Enter active/désactive l'interface de navigation
          if (!radioInterfaceActive) {
            // Toujours sélectionner la station en cours de lecture, même si on a navigué dans d'autres catégories
            if (currentRadioLink && radioCategories.length > 0) {
              (async () => {
                for (let catIdx = 0; catIdx < radioCategories.length; catIdx++) {
                  const res = await api.fetchRadioStations(radioCategories[catIdx].id);
                  const stations = typeof res !== 'string' && res.data.radio ? res.data.radio : [];
                  const playingIndex = stations.findIndex((st: { link: string }) => st.link === currentRadioLink);
                  if (playingIndex !== -1) {
                    setSelectedRadioCategoryIndex(catIdx);
                    setRadioStations(stations);
                    setSelectedRadioStationIndex(playingIndex);
                    break;
                  }
                }
              })();
            }
            setRadioInterfaceActive(true);
            setShowControls(false);
          } else {
            // Si l'interface Radio est active, sélectionner la station
            if (radioStations[selectedRadioStationIndex]) {
              const selectedStation = radioStations[selectedRadioStationIndex];
              // Ne pas recharger si c'est la même station que celle en cours
              if (selectedStation.link !== currentRadioLink) {
                switchToRadioStation(selectedStation);
              }
            }
          }
        } else {
          // Si les contrôles ne sont pas affichés ou si la vidéo est en cours de lecture
          if (!showControls) {
            // Mettre la vidéo en pause
            if (!isPaused) {
              handlePlayPause();
            }

            // Afficher les contrôles
            setShowControls(true);
            setNavigation('controls');
            setActiveControl('1_playpause'); // Sélectionner le bouton play/pause
            resetControlsTimeout();
            return;
          }

          // Si les contrôles sont déjà affichés et la vidéo est en pause
          // Exécuter l'action du contrôle actif
          if (navigation === 'controls') {
            // Dans handleKeyDown, ajoutez ces cas au switch
            switch (activeControl) {
              case '0_previous':
                handlePreviousEpisode();
                break;
              case '1_playpause':
                handlePlayPause();
                break;
              case '2_next':
                handleNextEpisode();
                break;
              case '3_audio':
                setNavigation('audio');
                // Utiliser la sélection mémorisée ou la première option
                const audioKeys = Object.keys(controlsConfig.audio || {});
                setActiveControl(selectedAudioTrack || audioKeys[0]);
                break;
              case '4_subtitle':
                setNavigation('subtitle');
                // Utiliser la sélection mémorisée ou la première option
                const subtitleKeys = Object.keys(controlsConfig.subtitle || {});
                setActiveControl(selectedSubtitleOption || subtitleKeys[0]);
                break;
            }
          } else if (navigation === 'audio' || navigation === 'subtitle') {
            // Sélectionner la piste audio ou sous-titre
            if (navigation === 'subtitle' && subtitleTracks.length > 0) {
              const newIndex = (currentSubtitleTrack + 1) % subtitleTracks.length;
              webapis.avplay.setSelectTrack("TEXT", subtitleTracks[newIndex].index);
              console.log("selected track TEXT INDEX:", subtitleTracks[newIndex].index)
              setCurrentSubtitleTrack(newIndex);
              // Mémoriser la sélection actuelle
              setSelectedSubtitleOption(activeControl);
              // Afficher les informations détaillées du stream
              let currentStreamInfo = webapis.avplay.getCurrentStreamInfo();
              console.log("Current Stream Info:", currentStreamInfo);

            } else if (navigation === 'audio') {
              const trackIndex = parseInt(activeControl.split('_')[0]) + 1;
              webapis.avplay.setSelectTrack("AUDIO", trackIndex);
              // Mémoriser la sélection actuelle
              setSelectedAudioTrack(activeControl);
            }
          } else if (navigation === 'seek') {
            // Appliquer le seek à la position choisie
            try {
              webapis.avplay.seekTo(seekPosition);
              setCurrentTime(seekPosition);
              setNavigation('controls');
              setSeekMode(false);
              setActiveControl('1_playpause');
            } catch (error) {
              console.error('Error seeking:', error);
            }
          }
        }
        break;
      case "ArrowLeft":
        if (isPlaying) {
          if (mediaType === 'tv' && tvInterfaceActive) {
            e.preventDefault();
            // Navigation horizontale dans les chaînes (sous-catégories)
            if (selectedChannelIndex > 0) {
              setSelectedChannelIndex(prev => prev - 1);
            }
          } else if (mediaType === 'tv' && !tvInterfaceActive) {
            e.preventDefault();
            // Passer à la chaîne précédente quand l'interface TV n'est pas active
            switchToPreviousChannel();
          } else if (mediaType === 'radio' && radioInterfaceActive) {
            e.preventDefault();
            // Navigation horizontale dans les stations radio
            if (selectedRadioStationIndex > 0) {
              setSelectedRadioStationIndex(prev => prev - 1);
            }
          } else if (showControls) {
            if (navigation === 'seek') {
              // En mode seek, commencer le recul continu
              startContinuousSeek('left');
            } else {
              handleControlNavigation('left');
            }
          } else {
            jumpBackward();
          }
        }
        break;
      case "ArrowRight":
        if (isPlaying) {
          if (mediaType === 'tv' && tvInterfaceActive) {
            e.preventDefault();
            // Navigation horizontale dans les chaînes (sous-catégories)
            if (selectedChannelIndex < tvChannels.length - 1) {
              setSelectedChannelIndex(prev => prev + 1);
            }
          } else if (mediaType === 'tv' && !tvInterfaceActive) {
            e.preventDefault();
            // Passer à la chaîne suivante quand l'interface TV n'est pas active
            switchToNextChannel();
          } else if (mediaType === 'radio' && radioInterfaceActive) {
            e.preventDefault();
            // Navigation horizontale dans les stations radio
            if (selectedRadioStationIndex < radioStations.length - 1) {
              setSelectedRadioStationIndex(prev => prev + 1);
            }
          } else if (showControls) {
            if (navigation === 'seek') {
              // En mode seek, commencer l'avance continue
              startContinuousSeek('right');
            } else {
              handleControlNavigation('right');
            }
          } else {
            jumpForward();
          }
        }
        break;
      case "ArrowUp":
        if (isPlaying) {
          if (mediaType === 'tv' && tvInterfaceActive && !(state?.channels && state.channels.length > 0)) {
            e.preventDefault();
            // Navigation verticale dans les catégories
            if (selectedCategoryIndex > 0) {
              const newIndex = selectedCategoryIndex - 1;
              setSelectedCategoryIndex(newIndex);
              if (tvCategories[newIndex]) {
                loadTVChannels(tvCategories[newIndex].id, false);
              }
            }
          } else if (mediaType === 'radio' && radioInterfaceActive) {
            e.preventDefault();
            // Navigation verticale dans les catégories radio
            if (selectedRadioCategoryIndex > 0) {
              const newIndex = selectedRadioCategoryIndex - 1;
              setSelectedRadioCategoryIndex(newIndex);
              if (radioCategories[newIndex]) {
                loadRadioStations(radioCategories[newIndex].id, false);
              }
            }
          } else if (showControls) {
            handleControlNavigation('up');
          }
        }
        break;
      case "ArrowDown":
        if (isPlaying) {
          if (mediaType === 'tv' && tvInterfaceActive && !(state?.channels && state.channels.length > 0)) {
            e.preventDefault();
            // Navigation verticale dans les catégories
            if (selectedCategoryIndex < tvCategories.length - 1) {
              const newIndex = selectedCategoryIndex + 1;
              setSelectedCategoryIndex(newIndex);
              if (tvCategories[newIndex]) {
                loadTVChannels(tvCategories[newIndex].id, false);
              }
            }
          } else if (mediaType === 'radio' && radioInterfaceActive) {
            e.preventDefault();
            // Navigation verticale dans les catégories radio
            if (selectedRadioCategoryIndex < radioCategories.length - 1) {
              const newIndex = selectedRadioCategoryIndex + 1;
              setSelectedRadioCategoryIndex(newIndex);
              if (radioCategories[newIndex]) {
                loadRadioStations(radioCategories[newIndex].id, false);
              }
            }
          } else if (showControls) {
            handleControlNavigation('down');
          }
        }
        break;
      case "i":
      case "Info":
        // Toggle EPG pour la TV
        if (mediaType === 'tv' && isPlaying) {
          setShowEPG(prev => !prev);
          e.preventDefault();
        }
        break;
      default:
        break;
    }
  };

  const handleControlNavigation = (direction: 'left' | 'right' | 'up' | 'down') => {
    if (!showControls) return;

    // Si nous sommes dans le mode de navigation principale des contrôles
    if (navigation === 'controls') {
      if (direction === 'left') {
        // Navigation horizontale dans la ligne du haut (previous, play/pause, next)
        if (activeControl === '1_playpause' && hasPrevious) {
          setActiveControl('0_previous');
        } else if (activeControl === '2_next') {
          setActiveControl('1_playpause');
        } else if (activeControl === '3_audio') {
          // Si on est sur audio/subtitle, aller à droite/gauche
          if (media?.sub && media.sub.trim()) {
            setActiveControl('4_subtitle');
          }
        } else if (activeControl === '4_subtitle') {
          setActiveControl('3_audio');
        }
      } else if (direction === 'right') {
        // Navigation horizontale dans la ligne du haut
        if (activeControl === '0_previous') {
          setActiveControl('1_playpause');
        } else if (activeControl === '1_playpause') {
          if (hasNext) {
            setActiveControl('2_next');
          } else {
            // Aller en mode seek si pas de bouton next
            setNavigation('seek');
            setSeekMode(true);
            setSeekPosition(currentTime);
          }
        } else if (activeControl === '2_next') {
          // Aller en mode seek depuis le bouton next
          setNavigation('seek');
          setSeekMode(true);
          setSeekPosition(currentTime);
        } else if (activeControl === '3_audio') {
          if (media?.sub && media.sub.trim()) {
            setActiveControl('4_subtitle');
          }
        } else if (activeControl === '4_subtitle') {
          setActiveControl('3_audio');
        }
      } else if (direction === 'down') {
        // Depuis les boutons centraux → aller vers le mode seek
        if (['0_previous', '1_playpause', '2_next'].includes(activeControl)) {
          setNavigation('seek');
          setSeekMode(true);
          setSeekPosition(currentTime);
        } else if (activeControl === '3_audio') {
          setNavigation('audio');
          setActiveControl(Object.keys(controlsConfig.audio || {})[0]);
        } else if (activeControl === '4_subtitle') {
          setNavigation('subtitle');
          setActiveControl(Object.keys(controlsConfig.subtitle || {})[0]);
        }
      } else if (direction === 'up') {
        // Aller de la ligne du bas vers la ligne du haut
        if (['3_audio', '4_subtitle'].includes(activeControl)) {
          setNavigation('seek');
          setSeekMode(true);
          setSeekPosition(currentTime);
        }
      }
    }
    // Si nous sommes dans un sous-menu (audio ou sous-titres)
    else if (navigation === 'audio' || navigation === 'subtitle') {
      const items = navigation === 'audio' ? controlsConfig.audio : controlsConfig.subtitle;
      const itemIds = Object.keys(items || {});
      const currentIndex = itemIds.findIndex(id => id === activeControl);

      if (direction === 'left') {
        // Aller à l'option précédente
        if (currentIndex > 0) {
          setActiveControl(itemIds[currentIndex - 1]);
        }
      } else if (direction === 'right') {
        // Aller à l'option suivante
        if (currentIndex < itemIds.length - 1) {
          setActiveControl(itemIds[currentIndex + 1]);
        }
      } else if (direction === 'up') {
        // Remonter au menu principal des contrôles
        setNavigation('controls');
        setActiveControl(navigation === 'audio' ? '3_audio' : '4_subtitle');
      }
    }
    // Si nous sommes en mode seek
    else if (navigation === 'seek') {
      if (direction === 'left') {
        // Reculer de 10 secondes
        const newPosition = Math.max(0, seekPosition - 10000);
        setSeekPosition(newPosition);
      } else if (direction === 'right') {
        // Avancer de 10 secondes
        const newPosition = Math.min(duration, seekPosition + 10000);
        setSeekPosition(newPosition);
      } else if (direction === 'up') {
        // Remonter vers les boutons centraux
        setNavigation('controls');
        setSeekMode(false);
        setActiveControl('1_playpause');
      } else if (direction === 'down') {
        // Descendre vers audio/subtitle depuis le mode seek
        setNavigation('controls');
        setSeekMode(false);
        if (media?.language && media.language.trim()) {
          setActiveControl('3_audio');
        } else if (media?.sub && media.sub.trim()) {
          setActiveControl('4_subtitle');
        } else {
          // Si pas d'audio/subtitle disponible, rester en mode seek
          setNavigation('seek');
          setSeekMode(true);
        }
      }
    }
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown as any);
    window.addEventListener("keyup", handleKeyUp as any);
    return () => {
      window.removeEventListener("keydown", handleKeyDown as any);
      window.removeEventListener("keyup", handleKeyUp as any);
      stopContinuousSeek(); // Nettoyer les intervalles
    };
  }, [navigate, isPlaying, playerReady, showControls, navigation, activeControl, seekPosition, duration,
    mediaType, tvInterfaceActive, selectedCategoryIndex, selectedChannelIndex, tvCategories, tvChannels, currentChannelLink,
    radioInterfaceActive, selectedRadioCategoryIndex, selectedRadioStationIndex, radioCategories, radioStations, currentRadioLink]);

  // Ajoutez ces variables avant le retour du composant
  let currentEpisodeIndex = -1;
  let hasPrevious = false;
  let hasNext = false;

  if (mediaType === 'quran' && state?.surahs) {
    // Pour le Coran, utiliser la liste des sourates
    currentEpisodeIndex = state.surahs.findIndex((surah: any) => surah.link === media.link);
    hasPrevious = currentEpisodeIndex > 0;
    hasNext = currentEpisodeIndex < state.surahs.length - 1;
  } else if (state?.episodes) {
    // Pour les séries/épisodes, utiliser la logique existante
    currentEpisodeIndex = state.episodes.findIndex((ep: SubCategory) => ep.link === media.link);
    hasPrevious = currentEpisodeIndex > 0;
    hasNext = currentEpisodeIndex < state.episodes.length - 1;
  }

  if (!media) return <div className="notFoundEpg">Media introuvable</div>;

  // Ajoutez cette fonction pour mettre à jour le temps de lecture de manière fiable
  const updatePlaybackTime = () => {
    try {
      if (typeof webapis !== 'undefined' && typeof webapis.avplay !== 'undefined' && isPlaying) {
        const state = webapis.avplay.getState();
        if (state === 'PLAYING' || state === 'PAUSED') {
          const newCurrentTime = webapis.avplay.getCurrentTime();
          const newDuration = webapis.avplay.getDuration();

          // Vérifier que les valeurs sont valides avant de mettre à jour l'état
          if (newCurrentTime >= 0 && !isNaN(newCurrentTime)) {
            setCurrentTime(newCurrentTime);
          }

          if (newDuration > 0 && !isNaN(newDuration)) {
            setDuration(newDuration);
          }
        }
      }
    } catch (error) {
      console.error('Error updating playback time:', error);
    }
  };

  const handlePlayPause = () => {
    const now = Date.now();
    // Vérifier si suffisamment de temps s'est écoulé depuis la dernière action (100ms minimum)
    if (now - lastActionTimeRef.current < 100) {
      return;
    }

    lastActionTimeRef.current = now;

    try {
      const currentState = webapis.avplay.getState();
      console.log('Current state before play/pause:', currentState);

      if (currentState === 'PLAYING') {
        webapis.avplay.pause();
        setIsPaused(true);
      } else if (currentState === 'PAUSED') {
        webapis.avplay.play();
        setIsPaused(false);
      }
    } catch (error) {
      console.error('Error toggling play/pause:', error);
      showErrorMessage('Erreur lors de la mise en pause/lecture.');
    }
  };

  // Ajoutez cet effet pour synchroniser périodiquement l'état de l'interface avec l'état réel du lecteur
  useEffect(() => {
    if (!isPlaying) return;

    // Vérifier l'état du lecteur toutes les 500ms
    const syncInterval = setInterval(() => {
      try {
        if (typeof webapis !== 'undefined' && typeof webapis.avplay !== 'undefined') {
          const currentState = webapis.avplay.getState();
          if (currentState === 'PAUSED' && !isPaused) {
            setIsPaused(true);
          } else if (currentState === 'PLAYING' && isPaused) {
            setIsPaused(false);
          }
        }
      } catch (error) {
        console.error('Error syncing player state:', error);
      }
    }, 500);

    return () => clearInterval(syncInterval);
  }, [isPlaying, isPaused]);

  // Ajoutez cet effet pour mettre à jour régulièrement le temps de lecture
  useEffect(() => {
    if (!isPlaying) {
      if (timeUpdateIntervalRef.current) {
        clearInterval(timeUpdateIntervalRef.current);
        timeUpdateIntervalRef.current = null;
      }
      return;
    }

    // Mettre à jour le temps toutes les 250ms
    timeUpdateIntervalRef.current = setInterval(updatePlaybackTime, 250);

    return () => {
      if (timeUpdateIntervalRef.current) {
        clearInterval(timeUpdateIntervalRef.current);
        timeUpdateIntervalRef.current = null;
      }
    };
  }, [isPlaying]);

  // Effet pour gérer l'affichage du message "Next Episode/Surah"
  useEffect(() => {
    if (!isPlaying || !duration || duration <= 0) {
      // Nettoyer si pas en lecture
      if (nextMessageTimeoutRef.current) {
        clearTimeout(nextMessageTimeoutRef.current);
        nextMessageTimeoutRef.current = null;
      }
      if (nextMessageIntervalRef.current) {
        clearInterval(nextMessageIntervalRef.current);
        nextMessageIntervalRef.current = null;
      }
      setShowNextMessage(false);
      return;
    }

    const timeRemaining = duration - currentTime;

    // Vérifier s'il y a un épisode/sourate suivant(e)
    let hasNext = false;

    if (mediaType === 'quran' && state?.surahs) {
      const currentSurahIndex = state.surahs.findIndex((surah: any) => surah.link === media.link);
      if (currentSurahIndex >= 0 && currentSurahIndex < state.surahs.length - 1) {
        hasNext = true;
      }
    } else if (state?.episodes) {
      const currentEpisodeIndex = state.episodes.findIndex((ep: SubCategory) => ep.link === media.link);
      if (currentEpisodeIndex >= 0 && currentEpisodeIndex < state.episodes.length - 1) {
        hasNext = true;
      }
    }

    // Afficher le message à 10 secondes de la fin si il y a un suivant
    if (hasNext && timeRemaining <= 10000 && timeRemaining > 0 && !showNextMessage) {
      setShowNextMessage(true);

      // Calculer le progrès initial basé sur le temps restant
      const initialProgress = ((10000 - timeRemaining) / 10000) * 100;
      setNextMessageProgress(Math.min(100, Math.max(0, initialProgress)));

      // Démarrer la barre de progression
      nextMessageIntervalRef.current = setInterval(() => {
        try {
          // Utiliser currentTime et duration du state au lieu d'appeler webapis directement
          const currentTimeRemaining = duration - currentTime;
          if (currentTimeRemaining <= 10000 && currentTimeRemaining > 0) {
            const progress = ((10000 - currentTimeRemaining) / 10000) * 100;
            setNextMessageProgress(Math.min(100, Math.max(0, progress)));
            console.log('Progress updated:', progress, 'Time remaining:', currentTimeRemaining);
          } else if (currentTimeRemaining <= 0) {
            // Fin atteinte, nettoyer
            setShowNextMessage(false);
            if (nextMessageIntervalRef.current) {
              clearInterval(nextMessageIntervalRef.current);
              nextMessageIntervalRef.current = null;
            }
          }
        } catch (error) {
          console.error('Error updating next message progress:', error);
        }
      }, 500); // Augmenter l'intervalle à 500ms pour plus de stabilité

      // Timeout pour cacher le message après 10 secondes
      nextMessageTimeoutRef.current = setTimeout(() => {
        setShowNextMessage(false);
        if (nextMessageIntervalRef.current) {
          clearInterval(nextMessageIntervalRef.current);
          nextMessageIntervalRef.current = null;
        }
      }, 10000);
    }

    // Mettre à jour la barre de progression si le message est affiché
    if (showNextMessage && duration > 0) {
      const currentTimeRemaining = duration - currentTime;
      if (currentTimeRemaining <= 10000 && currentTimeRemaining > 0) {
        const progress = ((10000 - currentTimeRemaining) / 10000) * 100;
        setNextMessageProgress(Math.min(100, Math.max(0, progress)));
      }
    }

    // Nettoyer si plus de temps ou pas de suivant
    if (!hasNext || timeRemaining > 10000) {
      if (showNextMessage) {
        setShowNextMessage(false);
        if (nextMessageTimeoutRef.current) {
          clearTimeout(nextMessageTimeoutRef.current);
          nextMessageTimeoutRef.current = null;
        }
        if (nextMessageIntervalRef.current) {
          clearInterval(nextMessageIntervalRef.current);
          nextMessageIntervalRef.current = null;
        }
      }
    }

    return () => {
      if (nextMessageTimeoutRef.current) {
        clearTimeout(nextMessageTimeoutRef.current);
      }
      if (nextMessageIntervalRef.current) {
        clearInterval(nextMessageIntervalRef.current);
      }
    };
  }, [isPlaying, duration, currentTime, mediaType, state?.episodes, state?.surahs, media.link, showNextMessage]);

  const jumpForward = () => {
    try {
      const currentState = webapis.avplay.getState();
      if (currentState === 'PLAYING' || currentState === 'PAUSED') {
        const successCallback = () => {
          console.log('Jump forward successful');
          // Mettre à jour le temps de lecture après un saut réussi
          setTimeout(() => {
            updatePlaybackTime();
          }, 100);
        };

        const errorCallback = (error: any) => {
          console.error('Jump forward error:', error);
        };

        webapis.avplay.jumpForward(10000, successCallback, errorCallback);
      }
    } catch (error) {
      console.error('Error in jumpForward:', error);
    }
  };

  const jumpBackward = () => {
    try {
      const currentState = webapis.avplay.getState();
      if (currentState === 'PLAYING' || currentState === 'PAUSED') {
        const successCallback = () => {
          console.log('Jump backward successful');
          // Mettre à jour le temps de lecture après un saut réussi
          setTimeout(() => {
            updatePlaybackTime();
          }, 100);
        };

        const errorCallback = (error: any) => {
          console.error('Jump backward error:', error);
        };

        webapis.avplay.jumpBackward(10000, successCallback, errorCallback);
      }
    } catch (error) {
      console.error('Error in jumpBackward:', error);
    }
  };

  // Fonctions pour le seek continu
  const startContinuousSeek = (direction: 'left' | 'right') => {
    // Arrêter tout seek en cours
    stopContinuousSeek();

    // Enregistrer la direction
    keyPressedRef.current = direction;

    // Faire le premier saut immédiatement
    if (direction === 'left') {
      const newPosition = Math.max(0, seekPosition - 10000);
      setSeekPosition(newPosition);
    } else {
      const newPosition = Math.min(duration, seekPosition + 10000);
      setSeekPosition(newPosition);
    }

    // Commencer l'intervalle pour les sauts continus
    seekIntervalRef.current = setInterval(() => {
      if (keyPressedRef.current === direction) {
        setSeekPosition(prev => {
          if (direction === 'left') {
            return Math.max(0, prev - 10000);
          } else {
            return Math.min(duration, prev + 10000);
          }
        });
      }
    }, 200); // Saut toutes les 200ms
  };

  const stopContinuousSeek = () => {
    if (seekIntervalRef.current) {
      clearInterval(seekIntervalRef.current);
      seekIntervalRef.current = null;
    }
    keyPressedRef.current = null;
  };

  // Gérer les événements keyup pour arrêter le seek continu
  const handleKeyUp = (e: KeyboardEvent) => {
    if (navigation === 'seek' && (e.key === 'ArrowLeft' || e.key === 'ArrowRight')) {
      stopContinuousSeek();
    }
  };

  // Fonctions pour charger les données TV
  const loadTVCategories = async () => {
    try {
      const response = await api.fetchLiveTV();
      if (typeof response !== 'string' && response.data.packages) {
        setTvCategories(response.data.packages);

        // Trouver la catégorie qui contient la chaîne actuelle
        await findAndLoadCurrentChannel(response.data.packages);
      }
    } catch (error) {
      console.error('Error loading TV categories:', error);
      showErrorMessage('Erreur lors du chargement des catégories TV. Veuillez réessayer.');
    }
  };

  const findAndLoadCurrentChannel = async (categories: TVCategory[]) => {
    // Optimisé : recherche parallèle dans toutes les catégories
    const searchLink = currentChannelLink || media.link;
    try {
      // Lancer tous les fetch en parallèle
      const results = await Promise.all(
        categories.map(cat =>
          api.fetchLiveTVChannels(cat.id)
            .then(res => ({
              cat,
              channels: typeof res !== 'string' && res.data.channels ? res.data.channels : [],
            }))
            .catch(() => ({ cat, channels: [] }))
        )
      );
      // Chercher la chaîne dans tous les résultats
      for (let i = 0; i < results.length; i++) {
        const { channels } = results[i];
        const channelIndex = channels.findIndex(channel => channel.ch === searchLink);
        if (channelIndex !== -1) {
          setSelectedCategoryIndex(i);
          setTvChannels(channels);
          setSelectedChannelIndex(channelIndex);
          return;
        }
      }
      // Si la chaîne n'est pas trouvée, charger la première catégorie par défaut
      if (categories.length > 0) {
        loadTVChannels(categories[0].id);
      }
    } catch (error) {
      console.error('Erreur lors de la recherche de la chaîne dans les catégories:', error);
      if (categories.length > 0) {
        loadTVChannels(categories[0].id);
      }
    }
  };

  const loadTVChannels = async (categoryId: string, preserveChannelIndex = false) => {
    try {
      const response = await api.fetchLiveTVChannels(categoryId);
      if (typeof response !== 'string' && response.data.channels) {
        setTvChannels(response.data.channels);
        if (!preserveChannelIndex) {
          setSelectedChannelIndex(0);
        } else {
          // S'assurer que l'index reste valide avec la nouvelle liste
          setSelectedChannelIndex(prev => Math.min(prev, response.data.channels.length - 1));
        }
      }
    } catch (error) {
      console.error('Error loading TV channels:', error);
      showErrorMessage('Erreur lors du chargement des chaînes. Veuillez réessayer.');
    }
  };

  const switchToChannel = (channel: any) => {
    if (typeof webapis !== 'undefined' && typeof webapis.avplay !== 'undefined') {
      try {
        webapis.avplay.stop();
        const channelLink = channel.ch || channel.link;
        webapis.avplay.open(channelLink);
        webapis.avplay.prepare();
        webapis.avplay.play();
        // Mettre à jour le lien de la chaîne courante
        setCurrentChannelLink(channelLink);
        setTvInterfaceActive(false);
      } catch (error) {
        console.error('Error switching channel:', error);
        showErrorMessage(`Erreur lors du changement vers ${channel.name}. Veuillez réessayer.`);
      }
    }
  };

  // Fonction pour passer à la chaîne suivante
  const switchToNextChannel = () => {
    const channelsToUse = (state?.channels && state.channels.length > 0 ? state.channels : tvChannels);
    if (channelsToUse.length === 0) return;

    // Trouver l'index de la chaîne actuelle
    let currentIndex = channelsToUse.findIndex((ch: any) => {
      const channelLink = ch.ch || ch.link;
      return channelLink === currentChannelLink;
    });

    // Si la chaîne actuelle n'est pas trouvée, commencer à 0
    if (currentIndex === -1) {
      currentIndex = 0;
    } else {
      // Passer à la chaîne suivante (boucler au début si on est à la fin)
      currentIndex = (currentIndex + 1) % channelsToUse.length;
    }

    const nextChannel = channelsToUse[currentIndex];
    if (nextChannel) {
      switchToChannel(nextChannel);
      setSelectedChannelIndex(currentIndex);
    }
  };

  // Fonction pour passer à la chaîne précédente
  const switchToPreviousChannel = () => {
    const channelsToUse = (state?.channels && state.channels.length > 0 ? state.channels : tvChannels);
    if (channelsToUse.length === 0) return;

    // Trouver l'index de la chaîne actuelle
    let currentIndex = channelsToUse.findIndex((ch: any) => {
      const channelLink = ch.ch || ch.link;
      return channelLink === currentChannelLink;
    });

    // Si la chaîne actuelle n'est pas trouvée, commencer à la fin
    if (currentIndex === -1) {
      currentIndex = channelsToUse.length - 1;
    } else {
      // Passer à la chaîne précédente (boucler à la fin si on est au début)
      currentIndex = currentIndex === 0 ? channelsToUse.length - 1 : currentIndex - 1;
    }

    const prevChannel = channelsToUse[currentIndex];
    if (prevChannel) {
      switchToChannel(prevChannel);
      setSelectedChannelIndex(currentIndex);
    }
  };

  // Charger les données TV lors du montage si c'est une TV
  useEffect(() => {
    if (mediaType === 'tv' && isPlaying) {
      loadTVCategories();
    }
  }, [mediaType, isPlaying]);

  // Fonctions pour charger les données Radio
  const loadRadioCategories = async () => {
    try {
      const response = await api.fetchRadio();
      if (typeof response !== 'string' && response.data.category) {
        setRadioCategories(response.data.category);

        // Trouver la catégorie qui contient la station actuelle
        await findAndLoadCurrentRadioStation(response.data.category);
      }
    } catch (error) {
      console.error('Error loading Radio categories:', error);
      showErrorMessage('Erreur lors du chargement des catégories Radio. Veuillez réessayer.');
    }
  };

  const findAndLoadCurrentRadioStation = async (categories: any[]) => {
    // Optimisé : recherche parallèle dans toutes les catégories radio
    const searchLink = currentRadioLink || media.link;
    try {
      // Lancer tous les fetch en parallèle
      const results = await Promise.all(
        categories.map(cat =>
          api.fetchRadioStations(cat.id)
            .then(res => ({
              cat,
              stations: typeof res !== 'string' && res.data.radio ? res.data.radio : [],
            }))
            .catch(() => ({ cat, stations: [] }))
        )
      );
      // Chercher la station dans tous les résultats
      for (let i = 0; i < results.length; i++) {
        const { stations } = results[i];
        const stationIndex = stations.findIndex(station => station.link === searchLink);
        if (stationIndex !== -1) {
          setSelectedRadioCategoryIndex(i);
          setRadioStations(stations);
          setSelectedRadioStationIndex(stationIndex);
          return;
        }
      }
      // Si la station n'est pas trouvée, charger la première catégorie par défaut
      if (categories.length > 0) {
        loadRadioStations(categories[0].id);
      }
    } catch (error) {
      console.error('Erreur lors de la recherche de la station dans les catégories radio:', error);
      if (categories.length > 0) {
        loadRadioStations(categories[0].id);
      }
    }
  };

  const loadRadioStations = async (categoryId: string, preserveStationIndex = false) => {
    try {
      const response = await api.fetchRadioStations(categoryId);
      if (typeof response !== 'string' && response.data.radio) {
        setRadioStations(response.data.radio);
        if (!preserveStationIndex) {
          setSelectedRadioStationIndex(0);
        } else {
          // S'assurer que l'index reste valide avec la nouvelle liste
          setSelectedRadioStationIndex(prev => Math.min(prev, response.data.radio.length - 1));
        }
      }
    } catch (error) {
      console.error('Error loading Radio stations:', error);
      showErrorMessage('Erreur lors du chargement des stations. Veuillez réessayer.');
    }
  };

  const switchToRadioStation = (station: any) => {
    if (typeof webapis !== 'undefined' && typeof webapis.avplay !== 'undefined') {
      try {
        webapis.avplay.stop();
        webapis.avplay.open(station.link);
        webapis.avplay.prepare();
        webapis.avplay.play();
        // Mettre à jour le lien de la station courante
        setCurrentRadioLink(station.link);
        setRadioInterfaceActive(false);
      } catch (error) {
        console.error('Error switching radio station:', error);
        showErrorMessage(`Erreur lors du changement vers ${station.name}. Veuillez réessayer.`);
      }
    }
  };

  // Charger les données Radio lors du montage si c'est une Radio
  useEffect(() => {
    if (mediaType === 'radio' && isPlaying) {
      loadRadioCategories();
    }
  }, [mediaType, isPlaying]);

  // Effet pour faire défiler automatiquement la liste des chaînes
  useEffect(() => {
    if (mediaType === 'tv' && tvInterfaceActive && tvChannelsContainerRef.current && tvChannels.length > 0) {
      const container = tvChannelsContainerRef.current;
      const selectedElement = container.children[selectedChannelIndex] as HTMLElement;

      if (selectedElement) {
        // Centrer l'élément sélectionné dans le conteneur horizontalement
        const containerWidth = container.clientWidth;
        const elementWidth = selectedElement.offsetWidth;
        const elementOffsetLeft = selectedElement.offsetLeft;
        // Calculer la position pour centrer l'élément
        const scrollTo = elementOffsetLeft - (containerWidth / 2) + (elementWidth / 2);
        container.scrollTo({ left: scrollTo, behavior: 'smooth' });
      }
    }
  }, [mediaType, tvInterfaceActive, selectedChannelIndex, tvChannels]);

  // Effet pour faire défiler automatiquement la liste des stations radio
  useEffect(() => {
    if (mediaType === 'radio' && radioInterfaceActive && radioStationsContainerRef.current && radioStations.length > 0) {
      const container = radioStationsContainerRef.current;
      const selectedElement = container.children[selectedRadioStationIndex] as HTMLElement;

      if (selectedElement) {
        // Centrer l'élément sélectionné dans le conteneur horizontalement
        const containerWidth = container.clientWidth;
        const elementWidth = selectedElement.offsetWidth;
        const elementOffsetLeft = selectedElement.offsetLeft;
        // Calculer la position pour centrer l'élément
        const scrollTo = elementOffsetLeft - (containerWidth / 2) + (elementWidth / 2);
        container.scrollTo({ left: scrollTo, behavior: 'smooth' });
      }
    }
  }, [mediaType, radioInterfaceActive, selectedRadioStationIndex, radioStations]);

  // Effet pour mettre à jour l'EPG quand la chaîne sélectionnée change
  useEffect(() => {
    if (mediaType === 'tv' && tvChannels.length > 0 && selectedChannelIndex >= 0 && selectedChannelIndex < tvChannels.length) {
      const selectedChannel = tvChannels[selectedChannelIndex];
      if (selectedChannel) {
        const epgData = parseEPGData(selectedChannel);
        setCurrentEPG(epgData);
        setShowEPG(true);
      }
    }
  }, [mediaType, tvChannels, selectedChannelIndex]);

  // Effet pour mettre à jour la progression de l'EPG
  useEffect(() => {
    if (mediaType === 'tv' && showEPG && currentEPG.current) {
      const updateProgress = () => {
        // Force re-render pour mettre à jour la barre de progression
        const now = Date.now();
        if (now >= currentEPG.current!.endTime) {
          // Le programme actuel est terminé, recharger l'EPG
          if (tvChannels.length > 0 && selectedChannelIndex >= 0 && selectedChannelIndex < tvChannels.length) {
            const selectedChannel = tvChannels[selectedChannelIndex];
            if (selectedChannel) {
              const epgData = parseEPGData(selectedChannel);
              setCurrentEPG(epgData);
            }
          }
        }
      };

      const interval = setInterval(updateProgress, 30000); // Mettre à jour toutes les 30 secondes
      return () => clearInterval(interval);
    }
  }, [mediaType, showEPG, currentEPG.current, tvChannels, selectedChannelIndex]);

  // Effet pour activer l'EPG quand l'interface TV devient active
  useEffect(() => {
    if (mediaType === 'tv' && tvInterfaceActive && tvChannels.length > 0 && selectedChannelIndex >= 0) {
      const selectedChannel = tvChannels[selectedChannelIndex];
      if (selectedChannel) {
        const epgData = parseEPGData(selectedChannel);
        setCurrentEPG(epgData);
      }
    }
  }, [mediaType, tvInterfaceActive, tvChannels, selectedChannelIndex]);

  return (
    <>
      {/* Spinner de chargement */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center z-50 bg-black/50">
          {/* Affichage de l'image de fond selon le type de media */}
          {mediaType === 'movie' && media?.back && (
            <img
              src={media.back}
              alt="background movie"
              className="absolute inset-0 w-full h-full object-cover opacity-40 pointer-events-none"
              style={{ zIndex: 0 }}
            />
          )}
          {mediaType === 'serie' && backlink && (
            <img
              src={backlink}
              alt="background serie"
              className="absolute inset-0 w-full h-full object-cover opacity-40 pointer-events-none"
              style={{ zIndex: 0 }}
            />
          )}
          <div className="loader"></div>
        </div>
      )}

      {/* Message d'erreur */}
      {errorMessage && (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/30 pointer-events-none">
          <div className="bg-red-600 text-white px-8 py-4 rounded-lg shadow-lg border border-red-500 max-w-md mx-auto pointer-events-auto">
            <div className="flex items-center gap-3">
              <p className="text-2xl font-medium">{errorMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* Message Next Episode/Surah */}
      {showNextMessage && (
        <div className="fixed bottom-32 right-8 z-50 bg-black/90 backdrop-blur-md text-white px-8 py-6 rounded-xl shadow-2xl border border-gray-500 max-w-md w-full">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-lg font-semibold text-blue-300 mb-1">
                {mediaType === 'quran' ? 'Sourate suivante' : 'Épisode suivant'}
              </p>
              <p className="text-sm text-gray-300 leading-tight">
                {(() => {
                  if (mediaType === 'quran' && state?.surahs) {
                    const currentSurahIndex = state.surahs.findIndex((surah: any) => surah.link === media.link);
                    if (currentSurahIndex >= 0 && currentSurahIndex < state.surahs.length - 1) {
                      return state.surahs[currentSurahIndex + 1].name || 'Sourate suivante';
                    }
                  } else if (state?.episodes) {
                    const currentEpisodeIndex = state.episodes.findIndex((ep: SubCategory) => ep.link === media.link);
                    if (currentEpisodeIndex >= 0 && currentEpisodeIndex < state.episodes.length - 1) {
                      return state.episodes[currentEpisodeIndex + 1].name || 'Épisode suivant';
                    }
                  }
                  return '';
                })()}
              </p>
            </div>
          </div>

          {/* Barre de progression améliorée */}
          <div className="mb-3">
            <div className="w-full bg-gray-700/60 rounded-full h-3 overflow-hidden">
              <div
                className="bg-gradient-to-r from-blue-500 to-blue-400 h-full rounded-full transition-all duration-100 ease-linear shadow-lg"
                style={{ width: `${nextMessageProgress}%` }}
              />
            </div>
          </div>

          <div className="flex justify-between items-center">
            <p className="text-xs text-gray-400">
              Lecture automatique dans
            </p>
            <p className="text-sm font-medium text-white bg-blue-600/30 px-2 py-1 rounded">
              {Math.ceil((100 - nextMessageProgress) / 10)}s
            </p>
          </div>
        </div>
      )}

      {/* Logo */}
      {((showControls && mediaType !== 'radio' && mediaType !== 'tv') || (mediaType === 'tv' && tvInterfaceActive) || (mediaType === 'radio' && radioInterfaceActive)) && (
        <Logo />
      )}


      {isPlaying && mediaType === 'tv' && (tvInterfaceActive || tvInterfaceLoading) && (
        <div className="fixed inset-0 z-40 bg-black bg-opacity-75 flex flex-col">
          {/* Zone principale pour la vidéo */}
          <div className="flex-1 flex">
            {/* Espace pour la vidéo */}
            <div className="flex-1"></div>
          </div>

          {/* Catégories à droite, centrées entre le haut et le haut de l'EPG */}
          {!(state?.channels && state.channels.length > 0) && (
            <div
              className="w-80 flex flex-col items-center p-6"
              style={{
                position: 'fixed',
                right: 0,
                top: 0,
                height: 'calc(100vh - 3.5rem - 2.5rem)',
                zIndex: 100,
                pointerEvents: 'auto',
                background: 'none',
                justifyContent: 'center',
              }}
            >
              {tvInterfaceLoading ? (
                <div className="flex flex-col items-center w-full animate-pulse">
                  <div className="bg-gray-800 rounded-lg h-32 w-32 mb-6"></div>
                  <div className="bg-gray-700 rounded-lg h-8 w-48 mb-4"></div>
                  <div className="bg-gray-700 rounded-lg h-8 w-32 mb-2"></div>
                  <div className="bg-gray-700 rounded-lg h-8 w-32"></div>
                </div>
              ) : (
                <div className="text-center space-y-4" >
                  {/* Catégorie précédente */}
                  {selectedCategoryIndex > 0 && (
                    <div className="p-1">
                      <div className="flex items-center gap-3 opacity-60">
                        {tvCategories[selectedCategoryIndex - 1]?.logo && (
                          <img
                            src={tvCategories[selectedCategoryIndex - 1].logo}
                            alt={tvCategories[selectedCategoryIndex - 1].name}
                            className="w-8 h-8 object-contain flex-shrink-0"
                          />
                        )}
                        <p className="text-sm text-gray-400 truncate">
                          {tvCategories[selectedCategoryIndex - 1]?.name}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Catégorie actuelle (sélectionnée) */}
                  <div className="bg-gray-800 bg-opacity-70 p-6 rounded-lg border-2 border-red-600">
                    <div className="flex items-center gap-4 mb-4">
                      {tvCategories[selectedCategoryIndex]?.logo && (
                        <img
                          src={tvCategories[selectedCategoryIndex].logo}
                          alt={tvCategories[selectedCategoryIndex].name}
                          className="w-16 h-16 object-contain flex-shrink-0"
                        />
                      )}
                      <h2 className="text-2xl font-bold text-white">
                        {tvCategories[selectedCategoryIndex]?.name || 'Catégorie'}
                      </h2>
                    </div>
                    <p className="text-gray-300 text-sm text-center">
                      {selectedCategoryIndex + 1} / {tvCategories.length}
                    </p>
                  </div>

                  {/* Catégorie suivante */}
                  {selectedCategoryIndex < tvCategories.length - 1 && (
                    <div className="p-1">
                      <div className="flex items-center gap-3 opacity-60">
                        {tvCategories[selectedCategoryIndex + 1]?.logo && (
                          <img
                            src={tvCategories[selectedCategoryIndex + 1].logo}
                            alt={tvCategories[selectedCategoryIndex + 1].name}
                            className="w-8 h-8 object-contain flex-shrink-0"
                          />
                        )}
                        <p className="text-sm text-gray-400 truncate">
                          {tvCategories[selectedCategoryIndex + 1]?.name}
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="mt-4 text-center">
                    <p className="text-gray-300 text-xs">
                      ↑ ↓ Changer catégorie
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* EPG (Electronic Program Guide) */}
          {(showEPG || tvInterfaceActive) && currentEPG && (currentEPG.current || currentEPG.next) && (
            <div className="p-6">
              <div className="w-full mx-auto">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Programme actuel */}
                  {currentEPG.current && (
                    <div className="bg-red-600 bg-opacity-80 p-4 rounded-lg border border-red-400">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-white font-bold text-lg">MAINTENANT</h4>
                        <div className="text-white text-sm">
                          {formatEPGTime(currentEPG.current.startTime)} - {formatEPGTime(currentEPG.current.endTime)}
                        </div>
                      </div>
                      <h5 className="text-white font-semibold text-base mb-2">{currentEPG.current.title}</h5>
                      <p className="text-gray-200 text-sm mb-3 line-clamp-3">{currentEPG.current.description}</p>

                      {/* Barre de progression */}
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-white h-2 rounded-full transition-all duration-1000"
                          style={{ width: `${getProgressPercentage(currentEPG.current)}%` }}
                        ></div>
                      </div>
                      <div className="text-right text-xs text-gray-300 mt-1">
                        {Math.round(getProgressPercentage(currentEPG.current))}% terminé
                      </div>
                    </div>
                  )}

                  {/* Programme suivant */}
                  {currentEPG.next && (
                    <div className="bg-gray-800 bg-opacity-80 p-4 rounded-lg border border-gray-600">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-gray-300 font-bold text-lg">ENSUITE</h4>
                        <div className="text-gray-300 text-sm">
                          {formatEPGTime(currentEPG.next.startTime)} - {formatEPGTime(currentEPG.next.endTime)}
                        </div>
                      </div>
                      <h5 className="text-white font-semibold text-base mb-2">{currentEPG.next.title}</h5>
                      <p className="text-gray-300 text-sm line-clamp-3">{currentEPG.next.description}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Barre des chaînes en bas */}
          <div className="h-32 p-4 mb-8">
            {tvInterfaceLoading ? (
              <div className="flex items-center justify-center h-full w-full animate-pulse">
                <div className="bg-gray-700 rounded-lg h-24 w-24 mx-2"></div>
                <div className="bg-gray-700 rounded-lg h-24 w-24 mx-2"></div>
                <div className="bg-gray-700 rounded-lg h-24 w-24 mx-2"></div>
                <div className="bg-gray-700 rounded-lg h-24 w-24 mx-2"></div>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-center h-full mb-6">
                  <div
                    ref={tvChannelsContainerRef}
                    className="flex gap-4 overflow-x-auto max-w-full px-4 scroll-smooth"
                    style={{ scrollBehavior: 'smooth' }}
                  >
                    {(state?.channels && state.channels.length > 0 ? state.channels : tvChannels).map((channel: any, index: any) => (
                      <div
                        key={channel.ch || channel.link || channel.id}
                        className={`flex-shrink-0 p-3 rounded-lg transition-all duration-200 min-w-[120px] ${selectedChannelIndex === index
                          ? 'scale-110'
                          : ''
                          }`}
                      >
                        <div className="flex flex-col items-center">
                          {channel.logo && (
                            <img
                              src={channel.logo}
                              alt={channel.name}
                              className="w-24 h-24 object-contain"
                            />
                          )}
                          <p className={`text-lg font-medium text-center line-clamp-2 ${selectedChannelIndex === index
                            ? 'text-red-400'
                            : 'text-white'
                            }`}>
                            {channel.name}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="text-center">
                  <p className="text-gray-300 text-xs">
                    ← → Naviguer entre les chaînes • Enter Sélectionner
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
      )}


      {/* Interface Radio */}
      {isPlaying && mediaType === 'radio' && radioInterfaceActive && (
        <div className="fixed inset-0 z-40 bg-black bg-opacity-75 flex flex-col">
          {/* Zone principale pour l'audio */}
          <div className="flex-1 flex">
            {/* Espace pour l'audio/spectre */}
            <div className="flex-1"></div>

            {/* Catégories à droite avec contexte vertical */}
            <div className="w-80 flex items-center justify-center p-6">
              <div className="text-center space-y-4">
                {/* Catégorie précédente */}
                {selectedRadioCategoryIndex > 0 && (
                  <div className="p-1">
                    <div className="flex items-center gap-3 opacity-60">
                      {radioCategories[selectedRadioCategoryIndex - 1]?.logo && (
                        <img
                          src={radioCategories[selectedRadioCategoryIndex - 1].logo}
                          alt={radioCategories[selectedRadioCategoryIndex - 1].name}
                          className="w-8 h-8 object-contain flex-shrink-0"
                        />
                      )}
                      <p className="text-sm text-gray-400 truncate">
                        {radioCategories[selectedRadioCategoryIndex - 1]?.name}
                      </p>
                    </div>
                  </div>
                )}

                {/* Catégorie actuelle (sélectionnée) */}
                <div className="bg-gray-800 bg-opacity-70 p-6 rounded-lg border-2 border-red-600">
                  <div className="flex items-center gap-4 mb-4">
                    {radioCategories[selectedRadioCategoryIndex]?.logo && (
                      <img
                        src={radioCategories[selectedRadioCategoryIndex].logo}
                        alt={radioCategories[selectedRadioCategoryIndex].name}
                        className="w-16 h-16 object-contain flex-shrink-0"
                      />
                    )}
                    <h2 className="text-2xl font-bold text-white">
                      {radioCategories[selectedRadioCategoryIndex]?.name || 'Catégorie'}
                    </h2>
                  </div>
                  <p className="text-gray-300 text-sm text-center">
                    {selectedRadioCategoryIndex + 1} / {radioCategories.length}
                  </p>
                </div>

                {/* Catégorie suivante */}
                {selectedRadioCategoryIndex < radioCategories.length - 1 && (
                  <div className="p-1">
                    <div className="flex items-center gap-3 opacity-60">
                      {radioCategories[selectedRadioCategoryIndex + 1]?.logo && (
                        <img
                          src={radioCategories[selectedRadioCategoryIndex + 1].logo}
                          alt={radioCategories[selectedRadioCategoryIndex + 1].name}
                          className="w-8 h-8 object-contain flex-shrink-0"
                        />
                      )}
                      <p className="text-sm text-gray-400 truncate">
                        {radioCategories[selectedRadioCategoryIndex + 1]?.name}
                      </p>
                    </div>
                  </div>
                )}

                <div className="mt-4 text-center">
                  <p className="text-gray-300 text-xs">
                    ↑ ↓ Changer catégorie
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Barre des stations en bas */}
          <div className="h-32 p-4 mb-8">
            <div className="flex items-center justify-center h-full mb-6">
              <div
                ref={radioStationsContainerRef}
                className="flex gap-4 overflow-x-auto max-w-full px-4 scroll-smooth"
                style={{ scrollBehavior: 'smooth' }}
              >
                {radioStations.map((station, index) => (
                  <div
                    key={station.id}
                    className={`flex-shrink-0 p-3 rounded-lg transition-all duration-200 min-w-[120px] ${selectedRadioStationIndex === index
                      ? 'scale-110'
                      : ''
                      }`}
                  >
                    <div className="flex flex-col items-center">
                      {station.logo && (
                        <img
                          src={station.logo}
                          alt={station.name}
                          className="w-24 h-24 object-contain"
                        />
                      )}
                      <p className={`text-lg font-medium text-center line-clamp-2 ${selectedRadioStationIndex === index
                        ? 'text-red-400'
                        : 'text-white'
                        }`}>
                        {station.name}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="text-center">
              <p className="text-gray-300 text-xs">
                ← → Naviguer entre les stations • Enter Sélectionner
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Spectre audio numérique pour les radios */}
      {mediaType === 'radio' && isPlaying && (
        <div className="fixed inset-0 flex items-center justify-center pointer-events-none">
          <div className="audio-spectrum-container">
            <div className="spectrum-title mb-4 text-center">
              <h2 className="text-2xl font-bold text-white mb-2">{media.name}</h2>
            </div>

            {/* Contrôles simplifiés pour radio */}
            <div className="radio-controls mb-4 text-center pointer-events-auto">
              <div className="radio-status-display">
                {isLoading ? (
                  <div className="loading-indicator">
                    <p className="text-lg text-blue-400 font-bold">CHARGEMENT</p>
                    <div className="loading-dots">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </div>
                ) : (
                  <div className="live-indicator">
                    <div className="live-dot"></div>
                    <p className="text-lg text-red-400 font-bold">LIVE</p>
                  </div>
                )}
                <p className="text-sm text-gray-300 mt-2">
                  {isLoading ? "Connexion au flux..." : "Streaming en direct"}
                </p>
              </div>
            </div>

            {isLoading ? (
              <div className="loading-spectrum">
                <p className="text-gray-400 text-center">
                  Chargement du flux audio...
                </p>
              </div>
            ) : (
              <div className="audio-spectrum flex items-center justify-center">
                <img src="/radio.gif" alt="Radio Animation" className="w-96 h-64" />
              </div>
            )}
            <div className="spectrum-info mt-4 text-center">
              <p className="text-sm text-gray-300">
                {formatPlayerTime(currentTime)} • Live Stream
              </p>
              <p className="text-xs text-gray-400 mt-1">
                Flux en direct • Enter pour naviguer • Retour pour quitter
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Boutons de contrôle centraux (style YouTube) */}
      {isPlaying && mediaType !== 'radio' && mediaType !== 'tv' && showControls && (
        <div className="fixed inset-0 flex items-center justify-center pointer-events-none z-20">
          <div className="flex items-center space-x-8 pointer-events-auto">
            {hasPrevious && (
              <div
                className={`play-btn ${activeControl === '0_previous' && navigation === 'controls' ? 'active-btn' : ''}`}
                id="0_previous"

              >
                <img className="w-32 h-32 p-2" src="/previous.svg" alt="previous" />
              </div>
            )}

            <div
              className={`play-btn ${activeControl === '1_playpause' && navigation === 'controls' ? 'active-btn' : ''}`}
              id="1_playpause"
            >
              <img
                className="w-32 h-32 p-2"
                src={isPaused ? "/play.svg" : "/pause.svg"}
                alt={isPaused ? "play" : "pause"}
              />
            </div>

            {hasNext && (
              <div
                className={`play-btn ${activeControl === '2_next' && navigation === 'controls' ? 'active-btn' : ''}`}
                id="2_next"

              >
                <img className="w-32 h-32 p-2" src="/next.svg" alt="next" />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Affichage de l'image de fond pour le Quran */}
      {mediaType === 'quran' && isPlaying && media?.dropback && (
        <div className="fixed inset-0 pointer-events-none bg-black/20 flex items-center justify-center">
          <img
            src={media.dropback}
            alt={media.name}
            className="max-w-full max-h-full object-contain"
          />
        </div>
      )}

      {/* Panneau de contrôle du lecteur */}
      {isPlaying && mediaType !== 'radio' && mediaType !== 'tv' && !showNextMessage && (
        <div
          className={`player-panel ${showControls ? 'visible' : 'hidden'}`}
          ref={playerPanelRef}
        >
          <div className="flex flex-col w-full">
            <div className="mb-4">
              <div className="text-lg mb-3">
                {media.name}
                {seekMode && (
                  <span className="text-sm text-yellow-400 ml-2">
                    [Cliquer sur Enter pour confirmer]
                  </span>
                )}
              </div>

              {/* Barre de temps */}
              <div className="mb-4">
                <div className="flex justify-between text-lg mb-1">
                  <div>{formatPlayerTime(navigation === 'seek' ? seekPosition : currentTime)}</div>
                  <div>{formatPlayerTime(duration)}</div>
                </div>
                <div className={`progress-bar ${navigation === 'seek' ? 'seek-mode' : ''}`}>
                  <div
                    className="playing"
                    style={{ width: `${duration > 0 ? ((navigation === 'seek' ? seekPosition : currentTime) / duration) * 100 : 0}%` }}
                  ></div>
                  {navigation === 'seek' && (
                    <div
                      className="seek-indicator"
                      style={{ left: `${duration > 0 ? (seekPosition / duration) * 100 : 0}%` }}
                    ></div>
                  )}
                </div>
              </div>

              {/* Ligne du bas avec audio, options audio, icône audio, icône subtitle, et options subtitle */}
              <div className="flex items-center justify-center">
                {/* Bloc audio */}
                <div className="relative flex items-center">
                  {/* Options audio à gauche de l'icône audio, position absolue */}
                  {media?.language && media.language.trim() && navigation === 'audio' && (
                    <div className="absolute right-full top-1/2 -translate-y-1/2 flex items-center space-x-2 mr-2 z-20">
                      {Object.entries(controlsConfig.audio || {}).map(([id, item]) => (
                        <div
                          key={id}
                          id={id}
                          className={`audio-option-inline px-3 py-1 rounded text-sm border ${activeControl === id ? 'active-btn' : 'border-gray-600 bg-gray-800'} ${selectedAudioTrack === id ? 'bg-green-600 border-green-400' : ''}`}
                        >
                          <span className="flex items-center">
                            {item}
                            {selectedAudioTrack === id && <span className="text-green-300 ml-2">✓</span>}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                  {media?.language && media.language.trim() && (
                    <div
                      className={`control-btn mx-4 ${activeControl === '3_audio' && navigation === 'controls' ? 'active-btn' : ''}`}
                      id="3_audio"
                    >
                      <img className="w-10 h-10" src="/audio.svg" alt="audio" />
                    </div>
                  )}
                </div>

                {/* Bloc subtitle */}
                <div className="relative flex items-center">
                  {media?.sub && media.sub.trim() && (
                    <div
                      className={`control-btn mx-4 ${activeControl === '4_subtitle' && navigation === 'controls' ? 'active-btn' : ''}`}
                      id="4_subtitle"
                    >
                      <img className="w-10 h-10" src="/subtitle.svg" alt="subtitle" />
                    </div>
                  )}
                  {/* Options subtitle à côté de l'icône subtitle, position absolue */}
                  {media?.sub && media.sub.trim() && navigation === 'subtitle' && (
                    <div className="absolute left-full top-1/2 -translate-y-1/2 flex items-center space-x-2 ml-2 z-20">
                      {Object.entries(controlsConfig.subtitle || {}).map(([id, item]) => (
                        <div
                          key={id}
                          id={id}
                          className={`subtitle-option-inline px-3 py-1 rounded text-sm border ${activeControl === id ? 'active-btn' : 'border-gray-600 bg-gray-800'} ${selectedSubtitleOption === id ? 'bg-green-600 border-green-400' : ''}`}
                        >
                          <span className="flex items-center">
                            {item}
                            {selectedSubtitleOption === id && <span className="text-green-300 ml-2">✓</span>}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Affichage des sous-titres */}
      {isPlaying && subtitles && subtitles.trim() && (
        <div className="fixed bottom-20 left-0 right-0 text-center z-30 px-4">
          <div className="text-white text-2xl font-sans bg-black bg-opacity-80 mx-auto inline-block px-6 py-3 rounded-lg shadow-lg border border-gray-600 max-w-4xl">
            <div className="whitespace-pre-wrap break-words leading-relaxed">
              {subtitles}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Player;