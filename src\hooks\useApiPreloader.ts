import { useEffect, useState } from 'react';
import { api, ApiResponse, CategoryApiResponse } from '../utils/api';
import { checkCacheValidity, getCachedData, isTizenAvailable, loadCacheFromTizen } from '../utils/tizenStorage';

export const useApiPreloader = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [isUsingCache, setIsUsingCache] = useState(false);

  useEffect(() => {
    const preloadAllData = async () => {
      try {
        setIsLoading(true);
        setProgress(10);
        
        // 1. Charger le cache si disponible
        if (isTizenAvailable()) {
          await loadCacheFromTizen();
        } else {
          console.log('Running in non-Tizen environment, skipping cache');
        }        
        // 2. Vérifier le cache pour les données principales
        const mainCacheKeys = [
          'movies_data',
          'series_data'
        ];
        
        let hasValidCache = await checkCacheValidity(mainCacheKeys);
        setProgress(30);
        
        // 3. Si cache principal valide, vérifier les caches spécifiques aux catégories
        if (hasValidCache) {
          // Récupérer les catégories depuis le cache
          const cachedMovies = await getCachedData<ApiResponse<CategoryApiResponse>>('movies_data');
          const cachedSeries = await getCachedData<ApiResponse<CategoryApiResponse>>('series_data');
          
          if (cachedMovies && cachedMovies.data?.category) {
            const categoryIds = cachedMovies.data.category.map(cat => cat.id);
            
            // Vérifier le cache pour chaque catégorie de films
            const categoryCacheChecks = await Promise.all(
              categoryIds.map(async id => ({
                id,
                hasCache: await checkCacheValidity([
                  `movies_by_category_${id}`,
                  `movies_list_${id}`
                ])
              }))
            );
            
            hasValidCache = categoryCacheChecks.every(check => check.hasCache);
          } else {
            hasValidCache = false;
          }
          
          // Vérifier le cache pour les séries
          if (hasValidCache && cachedSeries && cachedSeries.data?.category) {
            const seriesCategoryIds = cachedSeries.data.category.map(cat => cat.id);
            
            const seriesCacheChecks = await Promise.all(
              seriesCategoryIds.map(async id => ({
                id,
                hasCache: await checkCacheValidity([`series_list_${id}`])
              }))
            );
            
            hasValidCache = seriesCacheChecks.every(check => check.hasCache);
          } else {
            hasValidCache = false;
          }
        }
        
        if (hasValidCache) {
          console.log('Utilisation des données en cache');
          setIsUsingCache(true);
          setIsLoading(false);
          return;
        }
        
        // 4. Si pas de cache valide, faire les appels API
        console.log('Cache non disponible ou expiré, appel des APIs');
        
        // Préchargement des données principales
        const mainDataPromises = [
          api.fetchMovies(),
          api.fetchSeries(),
        ];
        
        setProgress(40);
        const results = await Promise.all(mainDataPromises);
        setProgress(60);

        // Préchargement des films (logique existante)
        if (typeof results[0] !== 'string') {
          const movieResponse = results[0].data as CategoryApiResponse;
          const movieCategories = movieResponse.category || [];
          
          const type1Categories = movieCategories.filter(cat => cat.type === "1");
          const otherCategories = movieCategories.filter(cat => cat.type !== "1");
          
          const type1Promises = type1Categories.map(category => 
            api.fetchMoviesByMovies(category.id)
          );
          
          setProgress(70);
          await Promise.all(type1Promises);
          
          const categoryPromises = otherCategories.map(category => 
            api.fetchMoviesByCategory(category.id)
          );
          
          setProgress(80);
          const categoryResults = await Promise.all(categoryPromises);
          
          const moviePromises = [];
          for (const result of categoryResults) {
            if (typeof result !== 'string') {
              const subCategories = result.data.category || [];
              const type1SubCategories = subCategories.filter(subCat => subCat.type === "1");
              
              for (const subCategory of type1SubCategories) {
                moviePromises.push(api.fetchMoviesByMovies(subCategory.id));
              }
            }
          }
          
          setProgress(90);
          await Promise.all(moviePromises);
        }

        // Préchargement simplifié des séries
        if (typeof results[1] !== 'string' && results[1].data?.category) {
          const seriesCategories = results[1].data.category;
          const seriesPromises = seriesCategories.map(category => 
            api.fetchSeriesBySeries(category.id)
          );
          await Promise.all(seriesPromises);
        }
        
        setProgress(100);
        setError(null);
      } catch (err) {
        console.error('Erreur lors du préchargement:', err);
        setError('Échec du chargement des données');
      } finally {
        setIsLoading(false);
      }
    };

    preloadAllData();
  }, []);

  return { isLoading, error, progress, isUsingCache };
};