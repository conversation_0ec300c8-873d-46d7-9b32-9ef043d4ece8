import { Movie } from "./movies";

export interface SeriesCategory {
  id: string;
  name: string;
  logo: string;
  thumb: string;
  description: string | null;
  category: string;
  actors: string | null;
  backlink: string;
  rate: string;
  date: string | null;
  type: string;
}

export interface SeriesApiResponse {
  category: SeriesCategory[];
  movies: Movie[];
}



export interface Episode {
  id: string;
  name: string;
  link: string;
  logo: string;
  thumb: string;
  dimension : string;
  type: string;
  duration: string;
  size: string;
  desc: string | null;
  actors: string | null;
  date: string | null;
}

export interface EpisodesApiResponse {
  episodes: Episode[];
}